from typing import Callable

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware


__all__ = ['ExceptionToResponseMiddleware']


class ExceptionToResponseMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            return await call_next(request)

        except HTTPException as e:
            return JSONResponse(status_code=e.status_code, content=e.detail, headers=e.headers)
