import logging
from typing import Sequence

from fastapi import APIRouter, HTTPException, Request, status

from constants.operation_ids import operation_ids
from dependencies import KXDashServiceDep
from exceptions import EntityNotFoundError
from schemas import DashTaskResponse


__all__ = ['router']

logger = logging.getLogger(__name__)

router = APIRouter(prefix='/dash')


@router.get(
    '/activities',
    operation_id=operation_ids.kx_dash.LIST,
)
async def list_activities(
    kx_dash_service: KXDashServiceDep,
    request: Request,
) -> Sequence[DashTaskResponse]:
    """
    List activities matching the query string.
    Maps to the external API endpoint: /api/dash/searchActivitiesForOwner

    Args:
        kx_dash_service: Injected KX Dash service

    Returns:
        List of matching activities
    """
    authorization_header_value: str = request.headers.get('Authorization', '')
    _, _, auth_token = authorization_header_value.partition(' ')
    try:
        activities = await kx_dash_service.list(auth_token)
        return activities
    except Exception:
        logger.exception('Error listing activities')
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail='Failed to list activities')


@router.get(
    '/activities/{activity_id}',
    operation_id=operation_ids.kx_dash.GET,
)
async def get_activity(activity_id: int, kx_dash_service: KXDashServiceDep, request: Request) -> DashTaskResponse:
    """
    Get an activity by its ID.
    Maps to the external API endpoint: /api/dash/getActivityDataById

    Args:
        activity_id: The ID of the activity to retrieve
        kx_dash_service: Injected KX Dash service

    Returns:
        Detailed activity information
    """
    authorization_header_value: str = request.headers.get('Authorization', '')
    _, _, auth_token = authorization_header_value.partition(' ')
    try:
        activity = await kx_dash_service.get(activity_id, auth_token)
        return activity
    except EntityNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception:
        logger.exception(f'Error retrieving activity {activity_id}')
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail='Failed to retrieve activity')
