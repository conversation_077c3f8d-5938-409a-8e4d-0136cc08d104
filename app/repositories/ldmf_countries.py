import logging

from config import settings
from dependencies import CustomAsyncClient
from schemas import CountryData


__all__ = ['LDMFCountriesRepository']

logger = logging.getLogger(__name__)


class LDMFCountriesRepository:
    """Repository for LDMF Countries API operations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the LDMF Countries Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = settings.ldmf_countries_api.base_url

    async def list(self, token: str) -> list[CountryData]:
        """
        Get a list of LDMF countries from the API.

        Args:
            token: Bearer token for authentication

        Returns:
            List of country data with format:
            [
                {'memberFirmId': 2470, 'name': 'Germany', 'id': 158},
                {'memberFirmId': 0, 'name': 'Chad', 'id': 141},
                {'memberFirmId': 2768, 'name': 'United States', 'id': 37}
            ]

        Raises:
            HTTPStatusError: If the API request fails
            TransportError: If there's a network error
        """
        url = self._base_path
        headers = {'Authorization': f'Bearer {token}'}
        response = await self._http_client.get(url, headers=headers)

        data = response.json()

        countries = [CountryData(**country) for country in data]
        return countries
