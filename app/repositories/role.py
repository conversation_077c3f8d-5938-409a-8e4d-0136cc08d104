import logging
from typing import Any

from config import settings
from core.urls import url_join
from dependencies import CustomAsyncClient


__all__ = ['RoleRepository']

logger = logging.getLogger(__name__)


class RoleRepository:
    """Repository for Role API operations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Role Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = settings.roles_api.base_url

    async def list(self, token: str) -> list[dict[str, Any]]:
        """
        List all roles.

        Returns:
            list[dict[str, Any]]: A list of roles

        Raises:
            Exception: If an error occurs while listing roles
        """
        url = url_join(self._base_path, 'project-roles')
        headers = {'Authorization': f'Bearer {token}'}
        try:
            return (await self._http_client.get(url, headers=headers)).json()

        except Exception as e:
            logger.error('Error listing roles: %s', e)
            raise e
