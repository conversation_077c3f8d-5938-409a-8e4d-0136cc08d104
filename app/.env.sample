# This configuration is intended to be used locally for development purposes.

ENVIRONMENT='local'
LOG_LEVEL='INFO'
DEBUG='false'

# CORS
# (configured for the DEV environment)
ALLOWED_HOSTS='https://kxnextgendevui.deloitteresources.com:4500'

# DB
# (configured for local usage)
DB_HOST='127.0.0.1'
DB_PORT='1433'
DB_USER='sa'
DB_PASSWORD='P@ssw0rd_2024_SQL_Secure!'
DB_NAME='genai_quals'
DB_DRIVER='ODBC+Driver+17+for+SQL+Server'

# Azure AD
# (configured for the DEV environment)
AZURE_AD_TENANT_ID='36da45f1-dd2c-4d1f-af13-5abe46b99921'
AZURE_AD_API_AUDIENCE='https://dev.kx.deloitte'
AZURE_AD_REQUIRED_SCOPES='access_as_user,user_impersonation'

# Azure Blob Storage
# (configured for local usage)
AZURE_STORAGE_CONNECTION_STRING='DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;'
AZURE_STORAGE_CONTAINER_NAME='documents'

# HTTP client
HTTP_CLIENT_TIMEOUT='30'
HTTP_CLIENT_FOLLOW_REDIRECTS='true'
HTTP_CLIENT_VERIFY_SSL='true'
HTTP_CLIENT_MAX_CONNECTIONS='100'
HTTP_CLIENT_MAX_KEEPALIVE_CONNECTIONS='20'

# KX Dash API
# (configured for the DEV environment)
KX_DASH_API_BASE_URL='https://devqualsappsai.deloitteresources.com/api/dash'

# Quals Clients API settings
# (configured for the DEV environment)
QUALS_API_BASE_URL='https://devquals.deloitteresources.com/api'

# Industries API settings
# (configured for the DEV environment)
INDUSTRIES_API_BASE_URL='https://devqualsappsai.deloitteresources.com/api/client'

# Services API settings
# (configured for the DEV environment)
SERVICES_API_BASE_URL='https://devqualsappsai.deloitteresources.com/api/project'

# Roles API settings
# (configured for the DEV environment)
ROLES_API_BASE_URL='https://devqualsappsai.deloitteresources.com/api/team-details'

# LDMF countries API settings
# (configured for the DEV environment)
LDMF_COUNTRIES_BASE_URL='https://devqualsappsai.deloitteresources.com/api/project/member-firms'

# Queue settings
# (configured for local usage)
AZURE_QUEUE_CONNECTION_STRING='DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;'
AZURE_CONTENT_QUEUE_NAME='content-analysis-queue'

# OpenAI settings
# (not configured)
AZURE_OPENAI_ENDPOINT='<PLACEHOLDER>'
AZURE_OPENAI_KEY='<PLACEHOLDER>'
AZURE_OPENAI_DEPLOYMENT='gpt-4o'
AZURE_OPENAI_MODEL='gpt-4o'
AZURE_OPENAI_API_VERSION='2024-08-01-preview'

# SignalR
# (not configured)
SIGNAL_R_CONNECTION_STRING='Endpoint=https://<PLACEHOLDER>;AccessKey=<PLACEHOLDER>;Version=<PLACEHOLDER>;'
