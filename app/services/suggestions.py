from dataclasses import dataclass
from functools import cached_property
import logging
from uuid import UUID

from fastapi import UploadFile

from constants.message import ConversationMessageIntention, SuggestedUserPrompt, SystemReplyType
from schemas.confirmed_data import ConfirmedData
from schemas.conversation_message.message import CombinedMessageSerializer, UserMessageSerializer
from schemas.extracted_data import AggregatedData


logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class SuggestedPromptsGenerator:
    """Detector for conversation message suggested prompts."""

    conversation_id: UUID
    user_message: UserMessageSerializer
    conversation_message_history: list[CombinedMessageSerializer]
    intention: ConversationMessageIntention
    aggregated_data: AggregatedData
    confirmed_data: ConfirmedData
    files: list[UploadFile] | None = None
    dash_task_activity_id: int | None = None
    current_reply_type: SystemReplyType | None = None

    async def run(self) -> list[SuggestedUserPrompt]:
        """Run the suggested prompts generator."""

        ##################
        # Get data used for cases checks
        aggregated_data = self.aggregated_data

        # Use current_reply_type if provided, otherwise fall back to conversation history
        latest_system_message_type = self.current_reply_type or self._latest_system_message_type
        user_message_as_suggested_reply: SuggestedUserPrompt | None = self.user_message.as_suggested_reply

        user_provide_input_or_client = (
            bool(self._cleaned_user_message) or bool(self.files) or bool(aggregated_data.client_name)
        )
        user_provide_input_or_ldmf_country = (
            bool(self._cleaned_user_message) or bool(self.files) or bool(aggregated_data.ldmf_country)
        )
        user_selects_brief_description = self.intention == ConversationMessageIntention.UNCERTAINTY
        user_selects_discard_dash = self.intention == ConversationMessageIntention.DASH_DISCARD

        ai_detected_multiple_client_name_variations = len(aggregated_data.client_name) > 1
        ai_detected_multiple_ldmf_country_variations = len(aggregated_data.ldmf_country) > 1
        ai_retrieves_single_client_name = len(aggregated_data.client_name) == 1
        ai_retrieves_single_ldmf_country = len(aggregated_data.ldmf_country) == 1
        ai_couldnt_find_provided_client_name = len(aggregated_data.client_name) == 0

        suggested_prompts = []

        if user_selects_discard_dash:
            return [SuggestedUserPrompt.UPLOAD_DOCUMENT, SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION]

        ##################
        # USE CASE 3, 4, 5 - Show me an example prompt
        if user_selects_brief_description:
            # 1. GIVEN the user is on the Prompt page
            # 2. WHEN the user selects the option "Write a brief description"
            # 3. VERIFY that the user can see the "Show me an example prompt" as a clickable option under the message
            suggested_prompts.append(SuggestedUserPrompt.SHOW_ME_AN_EXAMPLE_PROMPT)

        ##################
        # USE CASE 6, 7 - No, create my qual
        if (
            aggregated_data.all_fields_are_provided
            and self.confirmed_data.client_name
            and latest_system_message_type
            in [
                SystemReplyType.ADDITIONAL_DATA,
                SystemReplyType.ADDITIONAL_DATA_PROPOSAL,
                SystemReplyType.CONFIRMED_FIELDS_READY,
            ]
            and self._suggested_prompt_wasnt_used_yet(SuggestedUserPrompt.NO_CREATE_MY_QUAL)
            and user_message_as_suggested_reply != SuggestedUserPrompt.NO_CREATE_MY_QUAL
        ):
            # 1. GIVEN the user is on the Prompt page
            # 2. WHEN the user provides relevant info for all required fields to generate the qual
            # 3. VERIFY that the user can see the "No, create my qual" suggested prompt above the input
            # Extended conditions:

            # 1. All required fields are provided
            # 2. Client name is confirmed
            # 3. Previous system message was asking for additional data
            # 4. Prompt hasn't been used before
            # 5. Current user message is not "No, create my qual"
            suggested_prompts.append(SuggestedUserPrompt.NO_CREATE_MY_QUAL)

        ##################
        # USE CASE 8, 9 - Enter a new client
        if (
            user_provide_input_or_client
            and ai_detected_multiple_client_name_variations
            and latest_system_message_type
            in [
                SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS,
            ]
            and self._suggested_prompt_wasnt_used_yet(SuggestedUserPrompt.ENTER_A_NEW_CLIENT)
            and user_message_as_suggested_reply != SuggestedUserPrompt.ENTER_A_NEW_CLIENT
        ):
            # 1. GIVEN the user navigates to the Prompt page
            # 2. AND the user provides a prompt/uploads a document
            # 3. WHEN the AI detects a few variations of the Client name
            # 4. VERIFY that the user can see the "Enter a new client" suggested prompt above the input

            # Extended conditions:
            # 1. User provides a prompt/uploads a document
            # 2. AI detected multiple client name variations
            # 3. Previous system message was about multiple client name variations
            # 4. Prompt hasn't been used before
            # 5. Current user message is not "Enter a new client"
            suggested_prompts.append(SuggestedUserPrompt.ENTER_A_NEW_CLIENT)

        ##################
        # USE CASE 10, 11 - Yes, this is correct
        if (
            user_provide_input_or_client
            and ai_retrieves_single_client_name
            and latest_system_message_type
            in [
                SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION,
            ]
            and self._suggested_prompt_wasnt_used_yet(SuggestedUserPrompt.YES_THIS_IS_CORRECT)
            and user_message_as_suggested_reply != SuggestedUserPrompt.YES_THIS_IS_CORRECT
        ):
            # 1. GIVEN the user navigates to the Prompt page
            # 2. AND the user provides a prompt/uploads a document
            # 3. WHEN the AI retrieves a single result of the client's name
            # 4. VERIFY that the user can see the "Yes, this is correct" suggested prompt above the input
            suggested_prompts.append(SuggestedUserPrompt.YES_THIS_IS_CORRECT)

        ##################
        # USE CASE 12, 13 - No, I'll enter the client name
        if (
            user_provide_input_or_client
            and (ai_retrieves_single_client_name or ai_couldnt_find_provided_client_name)
            and latest_system_message_type
            in [
                SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION,
            ]
            and self._suggested_prompt_wasnt_used_yet(SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME)
            and user_message_as_suggested_reply
            not in [SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME, SuggestedUserPrompt.YES_THIS_IS_CORRECT]
        ):
            # 1. GIVEN the user navigates to the Prompt page
            # 2. AND the user provides a prompt/uploads a document
            # 3. WHEN the AI retrieves a single result of the client's name
            # 4. OR AI couldn't find the provided client name in the system
            # 5. VERIFY that the user can see the "No, I'll enter the client name" suggested prompt above the input
            suggested_prompts.append(SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME)

        ##################
        # USE CASE 14, 15 - Yes (for new client)
        if (
            user_provide_input_or_client
            and ai_couldnt_find_provided_client_name
            and latest_system_message_type
            in [
                SystemReplyType.CLIENT_NOT_FOUND,
            ]
            and self._suggested_prompt_wasnt_used_yet(SuggestedUserPrompt.YES)
            and user_message_as_suggested_reply != SuggestedUserPrompt.YES
        ):
            # 1. GIVEN the user navigates to the Prompt page
            # 2. AND the user provides a prompt/uploads a document
            # 3. WHEN the AI couldn't find the provided client name in the system (unique)
            # 4. VERIFY that the user can see the "Yes" suggested prompt above the input
            suggested_prompts.append(SuggestedUserPrompt.YES)

        ##################
        # USE CASE 16, 17 - No, I'll enter the Lead Deloitte Member Firm
        if (
            user_provide_input_or_ldmf_country
            and (ai_retrieves_single_ldmf_country or ai_detected_multiple_ldmf_country_variations)
            and latest_system_message_type
            in [
                SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS,
            ]
            and self._suggested_prompt_wasnt_used_yet(SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM)
            and user_message_as_suggested_reply != SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM
        ):
            # 1. GIVEN the user navigates to the Prompt page
            # 2. WHEN the user provides a prompt/uploads a document containing one
            # OR several possible Lead Deloitte Member Firm values
            # 3. VERIFY that the user can see the "No, I'll enter the Lead Deloitte Member Firm" suggested prompt above the input
            suggested_prompts.append(SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM)

        return suggested_prompts

    ################
    # utils & calculated properties

    @cached_property
    def _cleaned_user_message(self) -> str:
        """Get the cleaned user message."""
        return self.user_message.content.strip()

    @cached_property
    def _suggested_prompts_used_in_conversation(self) -> set[SuggestedUserPrompt]:
        """Get the suggested prompts used in the conversation."""
        used_prompts = set()

        if not self.conversation_message_history:
            return used_prompts

        for combined_message in self.conversation_message_history:
            as_suggested_reply: SuggestedUserPrompt | None = combined_message.user.as_suggested_reply
            if as_suggested_reply:
                used_prompts.add(as_suggested_reply)

        return used_prompts

    def _suggested_prompt_wasnt_used_yet(self, prompt: SuggestedUserPrompt) -> bool:
        """Check if a suggested prompt was used in the current conversation."""

        if prompt in self._suggested_prompts_used_in_conversation:
            return False

        return True

    @cached_property
    def _latest_combined_message(self) -> CombinedMessageSerializer | None:
        return self.conversation_message_history[-1] if self.conversation_message_history else None

    @cached_property
    def _latest_system_message(self) -> str | None:
        if self._latest_combined_message:
            assert self._latest_combined_message.system, (
                'Conversation message history is corrupted, found empty system message'
            )
            return self._latest_combined_message.system.content.strip()
        return None

    @cached_property
    def _latest_system_message_type(self) -> SystemReplyType | None:
        return (
            self._latest_combined_message.system.system_reply_type
            if self._latest_combined_message and self._latest_combined_message.system
            else None
        )

    @cached_property
    def _latest_user_message(self) -> str | None:
        return self._latest_combined_message.user.content.strip() if self._latest_combined_message else None
