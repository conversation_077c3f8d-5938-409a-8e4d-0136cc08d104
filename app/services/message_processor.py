from abc import ABC, abstractmethod
from dataclasses import dataclass
from functools import cached_property
import logging
from typing import Any, Optional, Protocol
from uuid import UUID


logger = logging.getLogger(__name__)

from fastapi import UploadFile

from constants.extracted_data import ConfirmedDataFields, ConversationState, DataSourceType, MissingDataStatus
from constants.message import ConversationMessageIntention, SuggestedUserPrompt, SystemReplyType
from exceptions import EntityNotFoundError
from repositories import ConversationRepository
from schemas import (
    ClientSearchRequest,
    ConversationMessageIntentClassifierServiceResponse,
    ConversationMessageProcessingResult,
    DatePickerOption,
)
from schemas.confirmed_data import ConfirmedData
from schemas.conversation_message.message import UserMessageSerializer
from schemas.extracted_data import AggregatedData

from .date_validator import DateValidatorService
from .document import DocumentService
from .extracted_data import ExtractedDataService
from .intent_classifier import IntentClassifierService


logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class ExtractionProcessingResult:
    """Result of uncertainty intention processing."""

    system_reply: str
    system_reply_type: SystemReplyType
    missing_data_status: MissingDataStatus
    next_expected_field: str | None = None
    missing_fields: list[str] | None = None
    options: list[str] | list[tuple[str | None, str | None]] | None = None
    conversation_state: Optional[ConversationState] = None


@dataclass(frozen=True)
class ProcessingContext:
    """Context object containing all data needed for message processing."""

    conversation_id: UUID
    user_message: UserMessageSerializer
    files: list[UploadFile] | None = None
    token: str = ''
    aggregated_data: Optional[AggregatedData] = None
    confirmed_data: Optional[ConfirmedData] = None


class IntentHandler(ABC):
    """Abstract base class for intent handlers following Single Responsibility Principle."""

    @abstractmethod
    async def can_handle(self, intention: ConversationMessageIntention, context: ProcessingContext) -> bool:
        """Check if this handler can process the given intention."""
        pass

    @abstractmethod
    async def handle(self, context: ProcessingContext) -> ExtractionProcessingResult | dict[str, Any]:
        """Handle the intent processing."""
        pass


class InputProcessor(ABC):
    """Abstract base class for input processors following Single Responsibility Principle."""

    @abstractmethod
    async def can_process(self, input_data: str, context: ProcessingContext) -> bool:
        """Check if this processor can handle the given input."""
        pass

    @abstractmethod
    async def process(self, input_data: str, context: ProcessingContext) -> ExtractionProcessingResult:
        """Process the input and return result."""
        pass


class ConversationStateManager(Protocol):
    """Protocol for conversation state management."""

    async def get_current_state(self, conversation_id: UUID) -> ConversationState:
        """Get current conversation state."""
        ...

    async def update_state(self, conversation_id: UUID, new_state: ConversationState) -> None:
        """Update conversation state."""
        ...


@dataclass(frozen=True)
class ClientNameInputProcessor(InputProcessor):
    """Processes client name input following Single Responsibility Principle."""

    extracted_data_service: ExtractedDataService

    async def can_process(self, input_data: str, context: ProcessingContext) -> bool:
        """Check if this processor can handle client name input."""
        if not context.confirmed_data:
            return False

        return context.confirmed_data.client_name is None and input_data.strip() != ''

    async def process(self, input_data: str, context: ProcessingContext) -> ExtractionProcessingResult:
        """Process client name input and return result."""
        client_name = input_data.strip()

        try:
            # Search for the client name using the quals client repository
            search_request = ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
            search_result = await self.extracted_data_service.quals_clients_repository.search_clients(
                search_request, context.token
            )

            if search_result.clients and len(search_result.clients) == 1:
                # Exactly one match found - auto-confirm
                confirmed_client_name = search_result.clients[0].name
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=context.conversation_id,
                    field_name=ConfirmedDataFields.CLIENT_NAME.value,
                    field_value=confirmed_client_name,
                    state=ConversationState.COLLECTING_COUNTRY,
                )

                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                confirmation_message = reply_type.message_text.format(client_name=confirmed_client_name)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                )

            elif search_result.clients and len(search_result.clients) > 1:
                # Multiple matches found - ask for confirmation
                reply_type = SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION
                confirmation_message = reply_type.message_text.format(client_name=client_name)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    options=[client_name],
                )

            else:
                # No matches found - ask if user wants to add as new client
                reply_type = SystemReplyType.CLIENT_NOT_FOUND
                confirmation_message = reply_type.message_text.format(client_name=client_name)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    options=[client_name],
                )

        except Exception as e:
            logger.error('Error handling client name input for conversation %s: %s', context.conversation_id, e)
            # Fallback to simple confirmation
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=context.conversation_id,
                field_name=str(ConfirmedDataFields.CLIENT_NAME),
                field_value=client_name,
                state=ConversationState.COLLECTING_COUNTRY,
            )

            reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
            confirmation_message = reply_type.message_text.format(client_name=client_name)
            return ExtractionProcessingResult(
                system_reply=confirmation_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
            )


@dataclass(frozen=True)
class DateInputProcessor(InputProcessor):
    """Processes date input following Single Responsibility Principle."""

    date_validator_service: DateValidatorService
    extracted_data_service: ExtractedDataService

    async def can_process(self, input_data: str, context: ProcessingContext) -> bool:
        """Check if this processor can handle date input."""
        return input_data.strip() != ''

    async def process(self, input_data: str, context: ProcessingContext) -> ExtractionProcessingResult:
        """Process date input and return result."""
        date_option, sure = await self._analyze_dates_message(input_data)
        start_date = date_option.start_date.isoformat() if date_option.start_date else None
        end_date = date_option.end_date.isoformat() if date_option.end_date else None

        if start_date and end_date and sure:
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=context.conversation_id,
                field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                field_value=(start_date, end_date),
                state=ConversationState.COLLECTING_OBJECTIVE,
            )
            reply_type = SystemReplyType.DATES_CONFIRMED
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.DATA_COMPLETE,
            )
        else:
            reply_type = SystemReplyType.DATES_AMBIGUOUS
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.COLLECTING_DATES,
                options=[(start_date, end_date)],
            )

    async def _analyze_dates_message(self, message: str) -> tuple[DatePickerOption, bool]:
        """Analyze message content and determine date option and certainty."""
        date_validator_response = await self.date_validator_service.validate_dates(user_message=message)

        start_date = date_validator_response.date_1
        end_date = date_validator_response.date_2

        sure = True
        for date in (start_date, end_date):
            if date and date.day <= 12:
                sure = False
                break

        # Sort dates if both are present
        if start_date and end_date and end_date < start_date:
            start_date, end_date = end_date, start_date

        return DatePickerOption(start_date=start_date, end_date=end_date), sure


@dataclass(frozen=True)
class CountryInputProcessor(InputProcessor):
    """Processes country input following Single Responsibility Principle."""

    extracted_data_service: ExtractedDataService

    async def can_process(self, input_data: str, context: ProcessingContext) -> bool:
        """Check if this processor can handle country input."""
        if not context.confirmed_data:
            return False

        return context.confirmed_data.ldmf_country is None and input_data.strip() != ''

    async def process(self, input_data: str, context: ProcessingContext) -> ExtractionProcessingResult:
        """Process country input and return result."""
        ldmf_country = input_data.strip()

        try:
            verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                ldmf_country, context.token
            )

            if verified_countries and len(verified_countries) == 1:
                verified_country = verified_countries[0]
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=context.conversation_id,
                    field_name=ConfirmedDataFields.LDMF_COUNTRY.value,
                    field_value=verified_country,
                    state=ConversationState.COLLECTING_DATES,
                )
                reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
                confirmation_message = reply_type.message_text.format(ldmf_country=verified_country)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                )

            elif verified_countries and len(verified_countries) > 1:
                # Multiple matches found
                reply_type = SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                    options=verified_countries,
                )

            else:
                # No matches found
                reply_type = SystemReplyType.LDMF_COUNTRY_SINGLE_CONFIRMATION
                confirmation_message = reply_type.message_text.format(ldmf_country=ldmf_country)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                    options=[ldmf_country],
                )

        except Exception as e:
            logger.error('Error handling country input for conversation %s: %s', context.conversation_id, e)
            # Fallback to simple confirmation
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=context.conversation_id,
                field_name=ConfirmedDataFields.LDMF_COUNTRY.value,
                field_value=ldmf_country,
                state=ConversationState.COLLECTING_COUNTRY,
            )
            reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
            confirmation_message = reply_type.message_text.format(ldmf_country=ldmf_country)
            return ExtractionProcessingResult(
                system_reply=confirmation_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
            )


@dataclass(frozen=True)
class ObjectiveInputProcessor(InputProcessor):
    """Processes objective and scope input following Single Responsibility Principle."""

    extracted_data_service: ExtractedDataService

    async def can_process(self, input_data: str, context: ProcessingContext) -> bool:
        """Check if this processor can handle objective input."""
        if not context.confirmed_data:
            return False

        return context.confirmed_data.objective_and_scope is None and input_data.strip() != ''

    async def process(self, input_data: str, context: ProcessingContext) -> ExtractionProcessingResult:
        """Process objective and scope input and return result."""
        objective_and_scope = input_data.strip()

        await self.extracted_data_service.update_confirmed_data(
            conversation_id=context.conversation_id,
            field_name=ConfirmedDataFields.OBJECTIVE_AND_SCOPE.value,
            field_value=objective_and_scope,
            state=ConversationState.COLLECTING_OUTCOMES,
        )

        reply_type = SystemReplyType.OBJECTIVE_AND_SCOPE_CONFIRMED
        confirmation_message = reply_type.message_text.format(objective_and_scope=objective_and_scope)
        return ExtractionProcessingResult(
            system_reply=confirmation_message,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.MISSING_DATA,
        )


@dataclass(frozen=True)
class OutcomesInputProcessor(InputProcessor):
    """Processes outcomes input following Single Responsibility Principle."""

    extracted_data_service: ExtractedDataService

    async def can_process(self, input_data: str, context: ProcessingContext) -> bool:
        """Check if this processor can handle outcomes input."""
        if not context.confirmed_data:
            return False

        return context.confirmed_data.outcomes is None and input_data.strip() != ''

    async def process(self, input_data: str, context: ProcessingContext) -> ExtractionProcessingResult:
        """Process outcomes input and return result."""
        outcomes = input_data.strip()

        await self.extracted_data_service.update_confirmed_data(
            conversation_id=context.conversation_id,
            field_name=ConfirmedDataFields.OUTCOMES.value,
            field_value=outcomes,
            state=ConversationState.DATA_COMPLETE,
        )

        reply_type = SystemReplyType.OUTCOMES_CONFIRMED
        confirmation_message = reply_type.message_text.format(outcomes=outcomes)
        return ExtractionProcessingResult(
            system_reply=confirmation_message,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.DATA_COMPLETE,
        )


@dataclass(frozen=True)
class UndefinedIntentHandler(IntentHandler):
    """Handles undefined intent following Single Responsibility Principle."""

    async def can_handle(self, intention: ConversationMessageIntention, context: ProcessingContext) -> bool:
        """Check if this handler can process undefined intention."""
        return intention == ConversationMessageIntention.UNDEFINED

    async def handle(self, context: ProcessingContext) -> dict[str, Any]:
        """Handle undefined intent processing."""
        reply_type = SystemReplyType.UNDEFINED
        return {'system_reply': reply_type.message_text, 'system_reply_type': reply_type}


@dataclass(frozen=True)
class ExampleIntentHandler(IntentHandler):
    """Handles example intent following Single Responsibility Principle."""

    async def can_handle(self, intention: ConversationMessageIntention, context: ProcessingContext) -> bool:
        """Check if this handler can process example intention."""
        return intention == ConversationMessageIntention.EXAMPLE

    async def handle(self, context: ProcessingContext) -> dict[str, Any]:
        """Handle example intent processing."""
        reply_type = SystemReplyType.EXAMPLE
        return {'system_reply': reply_type.message_text, 'system_reply_type': reply_type}


@dataclass(frozen=True)
class UncertaintyIntentHandler(IntentHandler):
    """Handles uncertainty intent following Single Responsibility Principle."""

    async def can_handle(self, intention: ConversationMessageIntention, context: ProcessingContext) -> bool:
        """Check if this handler can process uncertainty intention."""
        return intention in (ConversationMessageIntention.UNCERTAINTY, ConversationMessageIntention.NEED_CONTEXT)

    async def handle(self, context: ProcessingContext) -> dict[str, Any]:
        """Handle uncertainty intent processing."""
        if context.aggregated_data and context.aggregated_data.is_empty:
            reply_type = SystemReplyType.NEED_INFO_INITIAL
        else:
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
        return {'system_reply': reply_type.message_text, 'system_reply_type': reply_type}


@dataclass(frozen=True)
class DashDiscardIntentHandler(IntentHandler):
    """Handles dash discard intent following Single Responsibility Principle."""

    extracted_data_service: ExtractedDataService

    async def can_handle(self, intention: ConversationMessageIntention, context: ProcessingContext) -> bool:
        """Check if this handler can process dash discard intention."""
        return intention == ConversationMessageIntention.DASH_DISCARD

    async def handle(self, context: ProcessingContext) -> dict[str, Any]:
        """Handle dash discard intent processing."""
        await self.extracted_data_service.delete(context.conversation_id, DataSourceType.KX_DASH)
        reply_type = SystemReplyType.WELCOME_MESSAGE
        return {'system_reply': reply_type.message_text, 'system_reply_type': reply_type}


@dataclass(frozen=True)
class GenerateQualIntentHandler(IntentHandler):
    """Handles generate qual intent following Single Responsibility Principle."""

    extracted_data_service: ExtractedDataService
    conversation_repository: ConversationRepository

    async def can_handle(self, intention: ConversationMessageIntention, context: ProcessingContext) -> bool:
        """Check if this handler can process generate qual intention."""
        return intention == ConversationMessageIntention.GENERATE_QUAL

    async def handle(self, context: ProcessingContext) -> ExtractionProcessingResult:
        """Handle generate qual intent processing."""
        try:
            conversation = await self.conversation_repository.get(context.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(context.conversation_id))

            confirmed_data = await self.conversation_repository.get_confirmed_data(context.conversation_id)
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=context.conversation_id,
                token=context.token,
                confirmed_data=confirmed_data,
            )

            # Ask user for missing data
            if missing_data_response.status == MissingDataStatus.MISSING_DATA:
                await self.conversation_repository.update_state(
                    context.conversation_id, missing_data_response.conversation_state
                )
                return ExtractionProcessingResult(
                    system_reply=missing_data_response.message or '',
                    system_reply_type=SystemReplyType.MISSING_REQUIRED_DATA,
                    missing_data_status=missing_data_response.status,
                    next_expected_field=missing_data_response.next_expected_field,
                    missing_fields=missing_data_response.missing_fields,
                    options=missing_data_response.options,
                    conversation_state=missing_data_response.conversation_state,
                )

            # Ask user if they have any additional information to provide
            elif (
                missing_data_response.status == MissingDataStatus.DATA_COMPLETE
                and conversation.State.value != ConversationState.COLLECTING_ADDITIONAL_DATA.value
            ):
                await self.conversation_repository.update_state(
                    context.conversation_id, ConversationState.COLLECTING_ADDITIONAL_DATA
                )
                reply_type = SystemReplyType.ADDITIONAL_DATA
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.DATA_COMPLETE,
                )

            # User provided all the data, generate a qual
            else:
                await self.conversation_repository.update_state(context.conversation_id, ConversationState.QUAL_CREATED)
                reply_type = SystemReplyType.READY_TO_GENERATE_QUAL_REPLY
                confirmation_message = reply_type.message_text.format(client_name=confirmed_data.client_name)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.DATA_COMPLETE,
                )

        except Exception as e:
            logger.error('Exception in generate qual handler for conversation %s: %s', context.conversation_id, e)
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.ERROR,
            )


@dataclass(frozen=True)
class ExtractionIntentHandler(IntentHandler):
    """Handles extraction intent following Single Responsibility Principle."""

    extracted_data_service: ExtractedDataService
    conversation_repository: ConversationRepository
    input_processors: list[InputProcessor]

    async def can_handle(self, intention: ConversationMessageIntention, context: ProcessingContext) -> bool:
        """Check if this handler can process extraction intention."""
        return intention == ConversationMessageIntention.EXTRACTION

    async def handle(self, context: ProcessingContext) -> ExtractionProcessingResult:
        """Handle extraction intent processing."""
        user_message = context.user_message.content.strip()

        try:
            # Get current conversation state and confirmed data
            conversation = await self.conversation_repository.get(context.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(context.conversation_id))

            # Handle initial state with user message
            state_is_initial = str(conversation.State) == str(ConversationState.INITIAL)
            if state_is_initial and user_message:
                reply_type = SystemReplyType.EMPTY
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                )

            # Try to process input using specialized processors
            for processor in self.input_processors:
                if await processor.can_process(user_message, context):
                    return await processor.process(user_message, context)

            # Check what data is missing using the enhanced service
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=context.conversation_id,
                token=context.token,
                confirmed_data=context.confirmed_data,
            )

            # Update conversation state based on the response
            if missing_data_response.status == MissingDataStatus.MISSING_DATA:
                await self.conversation_repository.update_state(
                    context.conversation_id, missing_data_response.conversation_state
                )
                return ExtractionProcessingResult(
                    system_reply=missing_data_response.message or '',
                    system_reply_type=SystemReplyType.MISSING_REQUIRED_DATA,
                    missing_data_status=missing_data_response.status,
                    next_expected_field=missing_data_response.next_expected_field,
                    missing_fields=missing_data_response.missing_fields,
                    options=missing_data_response.options,
                    conversation_state=missing_data_response.conversation_state,
                )

            elif missing_data_response.status == MissingDataStatus.DATA_COMPLETE:
                await self.conversation_repository.update_state(
                    context.conversation_id, missing_data_response.conversation_state
                )
                reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=missing_data_response.status,
                )

            else:  # error status
                logger.error(
                    'Error in missing data collection for conversation %s: %s',
                    context.conversation_id,
                    missing_data_response.message,
                )
                print('Error in missing data collection for conversation %s: %s', context.conversation_id,
                      missing_data_response.message)
                reply_type = SystemReplyType.BRIEF_DESCRIPTION
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.ERROR,
                )

        except Exception as e:
            print('Exception in extraction handler for conversation %s: %s', context.conversation_id, e)
            logger.error('Exception in extraction handler for conversation %s: %s', context.conversation_id, e)
            # Fallback to original behavior on any error
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.ERROR,
            )


@dataclass(frozen=True)
class UserConfirmationIntentHandler(IntentHandler):
    """Handles user confirmation intent following Single Responsibility Principle."""

    extracted_data_service: ExtractedDataService
    conversation_repository: ConversationRepository
    input_processors: list[InputProcessor]

    async def can_handle(self, intention: ConversationMessageIntention, context: ProcessingContext) -> bool:
        """Check if this handler can process user confirmation intention."""
        return intention == ConversationMessageIntention.USER_CONFIRMATION

    async def handle(self, context: ProcessingContext) -> ExtractionProcessingResult:
        """Handle user confirmation intent processing."""
        user_message = context.user_message.content.strip()

        try:
            # Get current conversation state and confirmed data
            conversation = await self.conversation_repository.get(context.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(context.conversation_id))

            # Try to process input using specialized processors
            for processor in self.input_processors:
                if await processor.can_process(user_message, context):
                    return await processor.process(user_message, context)

            # Default confirmation response
            reply_type = SystemReplyType.FIELD_SAVED
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.DATA_COMPLETE,
            )

        except Exception as e:
            logger.error('Exception in user confirmation handler for conversation %s: %s', context.conversation_id, e)
            # Fallback to original behavior on any error
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.ERROR,
            )


@dataclass(frozen=True)
class ChangeEngagementDatesIntentHandler(IntentHandler):
    """Handles change engagement dates intent following Single Responsibility Principle."""

    conversation_repository: ConversationRepository

    async def can_handle(self, intention: ConversationMessageIntention, context: ProcessingContext) -> bool:
        """Check if this handler can process change engagement dates intention."""
        return intention == ConversationMessageIntention.CHANGE_ENGAGEMENT_DATES

    async def handle(self, context: ProcessingContext) -> ExtractionProcessingResult:
        """Handle change engagement dates intent processing."""
        conversation = await self.conversation_repository.get(context.conversation_id)
        if not conversation:
            raise EntityNotFoundError('Conversation', str(context.conversation_id))

        confirmed_data = await self.conversation_repository.get_confirmed_data(context.conversation_id)
        if not confirmed_data.date_intervals:
            await self.conversation_repository.update_state(context.conversation_id, ConversationState.COLLECTING_DATES)
            reply_type = SystemReplyType.DATES_AMBIGUOUS
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.COLLECTING_DATES,
                options=[(None, None)],
            )

        await self.conversation_repository.update_state(context.conversation_id, ConversationState.COLLECTING_DATES)
        reply_type = SystemReplyType.DATES_AMBIGUOUS
        return ExtractionProcessingResult(
            system_reply=reply_type.message_text,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.MISSING_DATA,
            conversation_state=ConversationState.COLLECTING_DATES,
            options=[confirmed_data.date_intervals],
        )


@dataclass(frozen=True)
class IntentHandlerFactory:
    """Factory for creating intent handlers following Open/Closed Principle."""

    extracted_data_service: ExtractedDataService
    conversation_repository: ConversationRepository
    input_processors: list[InputProcessor]

    def create_handlers(self) -> list[IntentHandler]:
        """Create all available intent handlers."""
        return [
            UndefinedIntentHandler(),
            ExampleIntentHandler(),
            UncertaintyIntentHandler(),
            DashDiscardIntentHandler(self.extracted_data_service),
            GenerateQualIntentHandler(self.extracted_data_service, self.conversation_repository),
            ExtractionIntentHandler(self.extracted_data_service, self.conversation_repository, self.input_processors),
            UserConfirmationIntentHandler(
                self.extracted_data_service, self.conversation_repository, self.input_processors
            ),
            ChangeEngagementDatesIntentHandler(self.conversation_repository),
        ]


@dataclass(frozen=True)
class InputProcessorFactory:
    """Factory for creating input processors following Open/Closed Principle."""

    extracted_data_service: ExtractedDataService
    date_validator_service: DateValidatorService

    def create_processors(self) -> list[InputProcessor]:
        """Create all available input processors."""
        return [
            ClientNameInputProcessor(self.extracted_data_service),
            DateInputProcessor(self.date_validator_service, self.extracted_data_service),
            CountryInputProcessor(self.extracted_data_service),
            ObjectiveInputProcessor(self.extracted_data_service),
            OutcomesInputProcessor(self.extracted_data_service),
        ]


@dataclass(frozen=True)
class MessageProcessingCoordinator:
    """Coordinates message processing workflow following Single Responsibility Principle."""

    intent_handlers: list[IntentHandler]
    input_processors: list[InputProcessor]

    async def process_intent(
        self, intention: ConversationMessageIntention, context: ProcessingContext
    ) -> ExtractionProcessingResult | dict[str, Any]:
        """Process intent using appropriate handler."""
        for handler in self.intent_handlers:
            if await handler.can_handle(intention, context):
                return await handler.handle(context)

        raise NotImplementedError(f'Intent {intention} not implemented')

    async def process_input(self, input_data: str, context: ProcessingContext) -> ExtractionProcessingResult | None:
        """Process input using appropriate processor."""
        for processor in self.input_processors:
            if await processor.can_process(input_data, context):
                return await processor.process(input_data, context)

        return None


@dataclass(frozen=True)
class ConversationMessageProcessor:
    """
    Refactored processor for conversation message intention following SOLID principles.

    This class now serves as an orchestrator that coordinates between different components
    rather than handling all processing logic itself.
    """

    conversation_id: UUID
    user_message: UserMessageSerializer
    intent_classifier_service: IntentClassifierService
    extracted_data_service: ExtractedDataService
    conversation_repository: ConversationRepository
    date_validator_service: DateValidatorService
    document_service: DocumentService
    files: list[UploadFile] | None = None
    token: str = ''

    @cached_property
    def _cleaned_user_message(self) -> str:
        """Get cleaned user message content."""
        return self.user_message.content.strip()

    @cached_property
    def _processing_context(self) -> ProcessingContext:
        """Create processing context for handlers."""
        return ProcessingContext(
            conversation_id=self.conversation_id,
            user_message=self.user_message,
            files=self.files,
            token=self.token,
        )

    @cached_property
    def _coordinator(self) -> MessageProcessingCoordinator:
        """Create message processing coordinator with all handlers."""
        input_factory = InputProcessorFactory(
            extracted_data_service=self.extracted_data_service,
            date_validator_service=self.date_validator_service,
        )
        input_processors = input_factory.create_processors()

        intent_factory = IntentHandlerFactory(
            extracted_data_service=self.extracted_data_service,
            conversation_repository=self.conversation_repository,
            input_processors=input_processors,
        )

        return MessageProcessingCoordinator(
            intent_handlers=intent_factory.create_handlers(),
            input_processors=input_processors,
        )

    async def _classify_intent(self) -> ConversationMessageIntention:
        """
        Classify user intent with pre-processing logic.

        Returns:
            The classified conversation message intention.
        """
        # Handle empty user message
        if not self._cleaned_user_message:
            return ConversationMessageIntention.EXTRACTION if self.files else ConversationMessageIntention.UNDEFINED

        # Handle suggested reply prompts
        suggested_reply = self.user_message.as_suggested_reply
        if suggested_reply:
            return self._map_suggested_reply_to_intent(suggested_reply)

        # Use LLM for intent classification
        intent_response = await self.intent_classifier_service.classify_intent(
            user_message=self._cleaned_user_message,
            response_cls=ConversationMessageIntentClassifierServiceResponse,
        )

        logger.info(f'Classified intention: {intent_response.intention}')
        return intent_response.intention

    def _map_suggested_reply_to_intent(self, suggested_reply: SuggestedUserPrompt) -> ConversationMessageIntention:
        """
        Map suggested user prompts to conversation intentions.

        Args:
            suggested_reply: The suggested user prompt to map.

        Returns:
            The corresponding conversation message intention.

        Raises:
            ValueError: For prompts that should be handled before intent classification.
            NotImplementedError: For unimplemented suggested prompts.
        """
        intent_mapping = {
            SuggestedUserPrompt.NO_CREATE_NEW_QUAL: ConversationMessageIntention.EXTRACTION,
            SuggestedUserPrompt.SHOW_ME_AN_EXAMPLE_PROMPT: ConversationMessageIntention.EXAMPLE,
            SuggestedUserPrompt.NO_CREATE_MY_QUAL: ConversationMessageIntention.GENERATE_QUAL,
            SuggestedUserPrompt.ENTER_A_NEW_CLIENT: ConversationMessageIntention.EXTRACTION,
            SuggestedUserPrompt.YES_THIS_IS_CORRECT: ConversationMessageIntention.USER_CONFIRMATION,
            SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME: ConversationMessageIntention.EXTRACTION,
            SuggestedUserPrompt.YES: ConversationMessageIntention.USER_CONFIRMATION,
        }

        # Handle special cases that should be processed before intent classification
        if suggested_reply in (
            SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION,
            SuggestedUserPrompt.UPLOAD_DOCUMENT,
        ):
            raise ValueError(
                f'Reply {suggested_reply} should be processed before intent classification. Please validate the logic.'
            )

        if suggested_reply not in intent_mapping:
            raise NotImplementedError(f'Suggested user prompt {suggested_reply} not implemented')

        return intent_mapping[suggested_reply]

    async def run(self) -> ConversationMessageProcessingResult:
        """
        Process the conversation message using the new architecture.

        This method now orchestrates the processing workflow using the coordinator
        and specialized handlers following SOLID principles.

        Returns:
            The processing result containing intention, system reply, and data.
        """
        # Handle special pre-processing case
        if self.user_message.as_suggested_reply == SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION:
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ConversationMessageProcessingResult(
                intention=ConversationMessageIntention.UNCERTAINTY,
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                data={},
            )

        # Prepare processing context with required data
        context = await self._prepare_processing_context()

        # Classify the user's intent (using backward compatible method)
        intention = await self._get_intent()

        # Process the intent using the coordinator
        result = await self._coordinator.process_intent(intention, context)

        # Convert result to the expected format
        return self._format_processing_result(intention, result)

    async def _prepare_processing_context(self) -> ProcessingContext:
        """
        Prepare processing context with aggregated and confirmed data.

        Returns:
            ProcessingContext with all required data loaded.
        """
        aggregated_data = await self.extracted_data_service.aggregate_data(self.conversation_id)
        confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)

        context = self._processing_context
        # Update context with loaded data (creating new instance since it's frozen)
        return ProcessingContext(
            conversation_id=context.conversation_id,
            user_message=context.user_message,
            files=context.files,
            token=context.token,
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
        )

    def _format_processing_result(
        self, intention: ConversationMessageIntention, result: ExtractionProcessingResult | dict[str, Any]
    ) -> ConversationMessageProcessingResult:
        """
        Format the processing result into the expected response format.

        Args:
            intention: The classified intention.
            result: The processing result from handlers.

        Returns:
            Formatted conversation message processing result.
        """
        if isinstance(result, ExtractionProcessingResult):
            # Convert dataclass to dict, excluding None values and system reply fields
            data_dict = {
                k: v
                for k, v in result.__dict__.items()
                if v is not None and k not in ('system_reply', 'system_reply_type')
            }
            return ConversationMessageProcessingResult(
                intention=intention,
                system_reply=result.system_reply,
                system_reply_type=result.system_reply_type,
                data=data_dict,
            )
        else:
            # Handle dict result format
            system_reply = result.pop('system_reply')
            system_reply_type = result.pop('system_reply_type')
            return ConversationMessageProcessingResult(
                intention=intention,
                system_reply=system_reply,
                system_reply_type=system_reply_type,
                data=result,
            )

    # Backward compatibility methods for tests
    async def _get_intent(self) -> ConversationMessageIntention:
        """Backward compatibility alias for _classify_intent."""
        return await self._classify_intent()

    async def _extract_data(self) -> ExtractionProcessingResult:
        """Backward compatibility method for extraction processing."""
        context = await self._prepare_processing_context()
        extraction_handler = ExtractionIntentHandler(
            self.extracted_data_service, self.conversation_repository, self._coordinator.input_processors
        )
        return await extraction_handler.handle(context)

    async def _user_confirmation(self) -> ExtractionProcessingResult:
        """Backward compatibility method for user confirmation processing."""
        context = await self._prepare_processing_context()
        confirmation_handler = UserConfirmationIntentHandler(
            self.extracted_data_service, self.conversation_repository, self._coordinator.input_processors
        )
        return await confirmation_handler.handle(context)

    async def _change_engagement_dates(self) -> ExtractionProcessingResult:
        """Backward compatibility method for change engagement dates processing."""
        context = await self._prepare_processing_context()
        dates_handler = ChangeEngagementDatesIntentHandler(self.conversation_repository)
        return await dates_handler.handle(context)

    async def _handle_data_complete_response(
        self, user_message: str, confirmed_data: ConfirmedData
    ) -> ExtractionProcessingResult:
        """Backward compatibility method for data complete response handling."""
        user_response = user_message.strip().lower()

        # Check if user wants to proceed with qual creation
        if any(keyword in user_response for keyword in ['no', 'create', 'generate', 'proceed', 'ready']):
            # Update conversation state to ready for qual creation
            await self.conversation_repository.update_state(
                self.conversation_id, ConversationState.READY_FOR_QUAL_CREATION
            )

            # Format the message with client name from confirmed data
            client_name = confirmed_data.client_name or 'the client'
            reply_type = SystemReplyType.READY_TO_CREATE_DRAFT_QUAL
            formatted_message = reply_type.message_text.format(client_name=client_name)
            return ExtractionProcessingResult(
                system_reply=formatted_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.DATA_COMPLETE,
            )

        # If user wants to add more information, keep them in DATA_COMPLETE state
        # and ask what they'd like to add
        else:
            reply_type = SystemReplyType.ADDITIONAL_DATA_PROPOSAL
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.DATA_COMPLETE,
            )
