from dataclasses import dataclass
import logging
from typing import cast

from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)

from config import settings
from constants.message import EXTRACT_DATES_SYSTEM_PROMPT, EXTRACT_DATES_USER_PROMPT
from repositories import OpenAIRepository
from schemas.dates import DatesLLMResponse


__all__ = ['DateValidatorService']


logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class DateValidatorService:
    """Validates and extracts date information from user messages."""

    openai_service: OpenAIRepository
    temperature: float = settings.openai.default_temperature

    @staticmethod
    def _create_system_message(message: str) -> ChatCompletionSystemMessageParam:
        """Create a system message for the chat completion."""
        return {'role': 'system', 'content': message}

    @staticmethod
    def _create_user_message(message: str) -> ChatCompletionUserMessageParam:
        """Create a user message for the chat completion."""
        return {'role': 'user', 'content': message}

    async def validate_dates(
        self,
        user_message: str,
    ) -> DatesLLMResponse:
        """
        Validate the user's message dates using an LLM.

        Args:
            user_message: The user's message content.

        Returns:
            The validated dates.
        """

        # Prepare the messages
        system_message = self._create_system_message(EXTRACT_DATES_SYSTEM_PROMPT)
        user_message_param = self._create_user_message(
            EXTRACT_DATES_USER_PROMPT.format(
                user_message=user_message,
            )
        )

        # Call the OpenAI API
        response = await self.openai_service.generate_chat_completion(
            messages=[
                cast(ChatCompletionMessageParam, system_message),
                cast(ChatCompletionMessageParam, user_message_param),
            ],
            temperature=self.temperature,
            response_format=DatesLLMResponse,
        )

        if not isinstance(response, DatesLLMResponse):
            raise RuntimeError(f'Invalid response from model, expected {DatesLLMResponse}, got {type(response)}')

        return response
