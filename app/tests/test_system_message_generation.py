from datetime import date

import pytest

from constants.extracted_data import ConversationState
from constants.message import OptionType, SystemReplyType
from schemas import AggregatedData, ConfirmedData
from services.system_message_generation import SystemMessageGenerationService


class TestSystemMessageGenerationService:
    """Test suite for SystemMessageGenerationService."""

    @pytest.fixture
    def service(self):
        """Create a SystemMessageGenerationService instance."""
        return SystemMessageGenerationService()

    @pytest.fixture
    def sample_aggregated_data(self):
        """Create sample aggregated data for testing."""
        return AggregatedData(
            client_name=['Test Client Inc.'],
            ldmf_country=['United States'],
            date_intervals=[('2024-01-01', '2024-12-31')],
            objective_and_scope='Test objective and scope content',
            outcomes='Test outcomes content',
        )

    @pytest.fixture
    def incomplete_aggregated_data(self):
        """Create incomplete aggregated data for testing."""
        return AggregatedData(
            client_name=['Test Client Inc.', 'Another Client'],
            ldmf_country=['United States'],
            date_intervals=[('2024-01-01', '2024-12-31')],
            objective_and_scope='Test objective and scope content',
            outcomes=None,  # Missing outcomes
        )

    @pytest.fixture
    def confirmed_data(self):
        """Create confirmed data for testing."""
        return ConfirmedData(
            date_intervals=('2024-01-01', '2024-12-31'),
            objective_and_scope='Test objective and scope content',
            outcomes='Test outcomes content',
        )

    @pytest.fixture
    def empty_aggregated_data(self):
        """Create empty aggregated data for testing."""
        return AggregatedData()

    def test_fgenerate_system_message_incomplete_data(self, service, incomplete_aggregated_data, confirmed_data):
        """Test formatting message when data is incomplete."""
        result = service.generate_system_message(incomplete_aggregated_data, confirmed_data)

        assert result == (
            SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS.message_text,
            SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS,
        )

    def test_generate_system_message_empty_data(self, service, empty_aggregated_data, confirmed_data):
        """Test formatting message when data is empty."""
        result = service.generate_system_message(empty_aggregated_data, confirmed_data)

        # Should still contain the introduction and next steps
        assert result == (SystemReplyType.NEED_INFO_INITIAL.message_text, SystemReplyType.NEED_INFO_INITIAL)

    def test_generate_options_client_names(self, service):
        """Test generating options when client names are available."""
        data = AggregatedData(client_name=['Client A', 'Client B'])
        confirmed_data = ConfirmedData()
        conversation_state = ConversationState.COLLECTING_CLIENT_NAME
        options = service.generate_options(data, confirmed_data, conversation_state)

        assert len(options) == 2
        assert options[0].type == OptionType.CLIENT_NAME
        assert options[0].client_name == 'Client A'
        assert options[1].type == OptionType.CLIENT_NAME
        assert options[1].client_name == 'Client B'

    def test_generate_options_ldmf_countries(self, service):
        """Test generating options when LDMF countries are available (no client names)."""
        data = AggregatedData(ldmf_country=['United States', 'Canada'])
        confirmed_data = ConfirmedData()
        conversation_state = ConversationState.COLLECTING_COUNTRY
        options = service.generate_options(data, confirmed_data, conversation_state)

        assert len(options) == 2
        assert options[0].type == OptionType.LDMF_COUNTRY
        assert options[0].ldmf_country == 'United States'
        assert options[1].type == OptionType.LDMF_COUNTRY
        assert options[1].ldmf_country == 'Canada'

    def test_generate_options_date_intervals(self, service):
        """Test generating options when date intervals are available (no client names or countries)."""
        data = AggregatedData(date_intervals=[('2024-01-01', '2024-12-31')])
        confirmed_data = ConfirmedData()
        conversation_state = ConversationState.COLLECTING_DATES
        options = service.generate_options(data, confirmed_data, conversation_state)

        assert len(options) == 1
        date_option = options[0]
        assert date_option.type == OptionType.DATES
        assert date_option.start_date.isoformat() == '2024-01-01'
        assert date_option.end_date.isoformat() == '2024-12-31'

    def test_generate_options_priority_order_client_names(self, service):
        """Test that options are generated in priority order (client names first)."""
        data = AggregatedData(
            client_name=['Test Client 1', 'Test Client 2'],
            ldmf_country=['United States', 'Canada'],
            date_intervals=[('2024-01-01', '2024-12-31')],
        )
        confirmed_data = ConfirmedData()
        conversation_state = ConversationState.COLLECTING_CLIENT_NAME
        options = service.generate_options(data, confirmed_data, conversation_state)

        # Should only return client name options (highest priority)
        assert len(options) == 2
        for opt in options:
            assert opt.type == OptionType.CLIENT_NAME

    def test_generate_options_priority_order_ldmf_countries(self, service):
        """Test that options are generated in priority order (ldmf countries first)."""
        data = AggregatedData(
            client_name=['Test Client 1'],
            ldmf_country=['United States', 'Canada'],
            date_intervals=[('2024-01-01', '2024-12-31')],
        )
        confirmed_data = ConfirmedData()
        conversation_state = ConversationState.COLLECTING_COUNTRY
        options = service.generate_options(data, confirmed_data, conversation_state)

        # Should only return ldmf country options (highest priority)
        assert len(options) == 2
        assert options[0].type == OptionType.LDMF_COUNTRY

    def test_generate_options_priority_order_dates(self, service):
        """Test that options are generated in priority order (dates first)."""
        data = AggregatedData(
            client_name=['Test Client 1'],
            ldmf_country=['United States'],
            date_intervals=[('2024-01-01', '2024-12-31')],
        )
        confirmed_data = ConfirmedData()
        conversation_state = ConversationState.COLLECTING_DATES
        options = service.generate_options(data, confirmed_data, conversation_state)

        # Should only return date options (highest priority)
        assert len(options) == 1
        assert options[0].type == OptionType.DATES

    def test_generate_options_empty_data(self, service, empty_aggregated_data):
        """Test generating options when no data is available."""
        options = service.generate_options(empty_aggregated_data)
        assert options == []

    def test_is_data_complete_true(self, sample_aggregated_data):
        """Test data completeness check when all fields are complete."""
        assert sample_aggregated_data.is_complete is True

    def test_is_data_complete_false_multiple_clients(self):
        """Test data completeness check when multiple client names exist."""
        data = AggregatedData(
            client_name=['Client A', 'Client B'],  # Multiple clients
            ldmf_country=['United States'],
            date_intervals=[('2024-01-01', '2024-12-31')],
            objective_and_scope='Test objective',
            outcomes='Test outcomes',
        )
        assert data.is_complete is False

    def test_is_data_complete_false_missing_outcomes(self):
        """Test data completeness check when outcomes are missing."""
        data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['United States'],
            date_intervals=[('2024-01-01', '2024-12-31')],
            objective_and_scope='Test objective',
            outcomes=None,  # Missing outcomes
        )
        assert data.is_complete is False

    def test_parse_date_string_valid(self, service):
        """Test parsing valid date string."""
        result = service._parse_date_string('2024-01-01')
        assert result == date(2024, 1, 1)

    def test_parse_date_string_invalid(self, service):
        """Test parsing invalid date string."""
        result = service._parse_date_string('invalid-date')
        assert result is None

    def test_parse_date_string_none(self, service):
        """Test parsing None date string."""
        result = service._parse_date_string(None)
        assert result is None
