import base64
from datetime import datetime, timedelta
import random
import secrets
from typing import Any, Generator
from unittest.mock import AsyncMock, patch
from uuid import uuid4

import pytest

from config import settings


__all__ = ['decoded_jwt_token', 'auth_header', 'auth_mock']


_NOW = datetime.now()


def _get_random_ipv4():
    return '.'.join(str(random.randint(0, 255)) for _ in range(4))


def _get_random_onprem_sid():
    p1 = random.randint(10, 99)
    p2 = random.randint(100000000, 999999999)
    p3 = random.randint(1000000000, 9999999999)
    p4 = random.randint(1000000000, 9999999999)
    p5 = random.randint(1000000, 9999999)
    return f'S-1-5-{p1}-{p2}-{p3}-{p4}-{p5}'


def _get_random_secret(length):
    return base64.urlsafe_b64encode(secrets.token_bytes(length)).decode('utf-8').rstrip('=')


@pytest.fixture(scope='session')
def decoded_jwt_token() -> dict[str, Any]:
    return {
        'aud': settings.auth.ad.api_audience,
        'iss': f'https://sts.windows.net/{settings.auth.ad.aad_tenant_id}/',
        'iat': int(_NOW.timestamp()) - 1,
        'nbf': int(_NOW.timestamp()) - 1,
        'exp': int((_NOW + timedelta(days=1)).timestamp()),
        'acr': '1',
        'aio': 'some_aio',
        'amr': ('pwd', 'rsa'),
        'appid': str(uuid4()),
        'appidacr': '0',
        'deviceid': str(uuid4()),
        'email': '<EMAIL>',
        'family_name': 'Doe',
        'given_name': 'John',
        'ipaddr': _get_random_ipv4(),
        'name': 'Doe, John',
        'oid': str(uuid4()),
        'onprem_sid': _get_random_onprem_sid(),
        'rh': '1.' + _get_random_secret(40) + '.',
        'scp': 'access_as_user',
        'sid': str(uuid4()),
        'sub': _get_random_secret(32),
        'tid': settings.auth.ad.aad_tenant_id,
        'unique_name': '<EMAIL>',
        'upn': '<EMAIL>',
        'uti': _get_random_secret(16),
        'ver': '1.0',
    }


@pytest.fixture(scope='session')
def auth_header() -> dict[str, str]:
    return {'Authorization': 'Bearer SOME_TEST_TOKEN'}


@pytest.fixture
def auth_mock(decoded_jwt_token) -> Generator[tuple[AsyncMock, AsyncMock], None, None]:
    """Fixture that patches authentication methods of AzureADAuthorizerMiddleware.

    Returns:
        Mocks for `_validate_token_scopes` and `_decode_token`
    """

    with (
        patch('middleware.auth.AzureADAuthorizerMiddleware._validate_token_scopes') as validate_token_scopes_mock,
        patch('middleware.auth.AzureADAuthorizerMiddleware._decode_token') as decode_token_mock,
    ):
        decode_token_mock.new_callable = AsyncMock
        decode_token_mock.return_value = decoded_jwt_token
        yield validate_token_scopes_mock, decode_token_mock
