from unittest.mock import AsyncMock, patch

import pytest

from repositories.kx_dash import KXDashRepository
from schemas import DashTaskResponse
from services.kx_dash import KXDashService


__all__ = [
    'mock_activity',
    'mock_activities_response',
    'kx_dash_service_with_repo',
    'kx_dash_repository',
    'extracted_data_service_mock',
    'mock_kx_dash_service',
]


@pytest.fixture(autouse=True)
def mock_kx_dash_service():
    """Auto-use fixture that mocks KX Dash service to prevent network calls."""
    with patch('services.kx_dash.KXDashService.list') as mock_list:
        # Return empty list by default - tests can override this if needed
        mock_list.return_value = []
        yield mock_list


@pytest.fixture
def kx_dash_repository():
    """Mock KX Dash repository"""
    mock = AsyncMock()
    mock.list = AsyncMock()
    mock.get = AsyncMock()
    mock.MOCKED_TASKS = KXDashRepository.MOCKED_TASKS
    return mock


@pytest.fixture
def extracted_data_service_mock():
    """Mock extracted data service"""
    mock = AsyncMock()
    mock.list = AsyncMock(return_value=[])
    mock.update = AsyncMock()
    mock.aggregate_data = AsyncMock()
    mock.list_dash_tasks = AsyncMock(return_value=[])
    return mock


@pytest.fixture
def kx_dash_service_with_repo(kx_dash_repository, extracted_data_service_mock):
    """KX Dash service with mocked repository and extracted data service"""
    service = KXDashService(kx_dash_repository=kx_dash_repository, extracted_data_service=extracted_data_service_mock)

    # Configure the extracted_data_service_mock behavior
    extracted_data_service_mock.aggregate_data.return_value = None
    extracted_data_service_mock.update.return_value = None

    return service


@pytest.fixture
def mock_activity():
    """Fixture providing mock activity data"""
    return {
        'activityId': 100001,
        'activityName': 'Mock Quality Review 100001',
        'clientName': 'Mock Client Corporation',
        'memberFirm': 'US',
        'country': 'United States',
        'globalBusiness': 'Audit & Assurance',
        'globalBusinessServiceArea': 'Audit',
        'globalBusinessServiceLine': 'External Audit',
        'globalIndustry': 'Financial Services',
        'globalIndustrySector': 'Banking',
        'engagementCode': 'ENG-100001',
        'globalLCSPEmails': ['<EMAIL>', '<EMAIL>'],
        'engagementLepEmails': ['<EMAIL>', '<EMAIL>'],
        'engagementManagerEmails': ['<EMAIL>', '<EMAIL>'],
        'activityOwnerEmails': ['<EMAIL>', '<EMAIL>'],
        'engagementStartDate': '2024-04-01',
        'engagementEndDate': '2024-06-30',
        'dueDate': '2025-04-19',
    }


@pytest.fixture
def mock_activities_response(mock_activity):
    """Fixture providing mock activities list response"""
    return [DashTaskResponse.model_validate(mock_activity)]
