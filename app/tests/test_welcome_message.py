from unittest.mock import AsyncMock
from uuid import UUID

import pytest

from constants.message import (
    WELCOME_MESSAGE,
    WELCOME_MESSAGE_WITH_MANY_DASH_TASKS,
    WELCOME_MESSAGE_WITH_ONE_DASH_TASK,
    MessageRole,
    MessageType,
    SystemReplyType,
)
from schemas.conversation_message import MessageValidator
from services.conversation import ConversationService


class TestWelcomeMessage:
    @pytest.mark.asyncio
    async def test_create_welcome_message_with_multiple_dash_tasks(self):
        mock_conversation_message_service = AsyncMock()
        mock_kx_dash_service = AsyncMock()
        conversation_service = ConversationService(
            conversation_repository=AsyncMock(),
            conversation_message_service=mock_conversation_message_service,
            extracted_data_service=AsyncMock(),
            document_service=AsyncMock(),
            kx_dash_service=mock_kx_dash_service,
        )

        conversation_id = UUID('00000000-0000-0000-0000-000000000000')

        mock_kx_dash_tasks = [
            AsyncMock(activity_id=1, client_name='Client 1', engagement_code='123'),
            AsyncMock(activity_id=2, client_name='Client 2', engagement_code='234'),
        ]
        mock_kx_dash_service.list.return_value = mock_kx_dash_tasks

        mock_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=WELCOME_MESSAGE_WITH_MANY_DASH_TASKS,
            options=(),
            suggested_prompts=[],
            system_reply_type=SystemReplyType.WELCOME_MESSAGE_WITH_MANY_DASH_TASKS,
        )
        mock_conversation_message_service.create_message.return_value = mock_message

        result = await conversation_service._create_welcome_message(conversation_id, token='')

        mock_kx_dash_service.list.assert_awaited_once()
        mock_conversation_message_service.create_message.assert_awaited_once()

        assert result.content == WELCOME_MESSAGE_WITH_MANY_DASH_TASKS
        assert result.suggested_prompts == []

    @pytest.mark.asyncio
    async def test_create_welcome_message_with_one_dash_task(self):
        mock_conversation_message_service = AsyncMock()
        mock_kx_dash_service = AsyncMock()
        conversation_service = ConversationService(
            conversation_repository=AsyncMock(),
            conversation_message_service=mock_conversation_message_service,
            extracted_data_service=AsyncMock(),
            document_service=AsyncMock(),
            kx_dash_service=mock_kx_dash_service,
        )

        conversation_id = UUID('00000000-0000-0000-0000-000000000000')

        mock_kx_dash_tasks = [AsyncMock(activity_id=1, client_name='Client 1', engagement_code='123')]
        mock_kx_dash_service.list.return_value = mock_kx_dash_tasks

        mock_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=WELCOME_MESSAGE_WITH_ONE_DASH_TASK,
            options=(),
            suggested_prompts=['Upload a document', 'Write a brief description'],
            system_reply_type=SystemReplyType.WELCOME_MESSAGE_WITH_ONE_DASH_TASK,
        )
        mock_conversation_message_service.create_message.return_value = mock_message

        result = await conversation_service._create_welcome_message(conversation_id, token='')

        mock_kx_dash_service.list.assert_awaited_once()
        mock_kx_dash_service.on_select.assert_awaited_once()
        mock_conversation_message_service.create_message.assert_awaited_once()

        assert result.content == WELCOME_MESSAGE_WITH_ONE_DASH_TASK
        assert result.suggested_prompts == ['Upload a document', 'Write a brief description']

    @pytest.mark.asyncio
    async def test_create_welcome_message_without_dash_tasks(self):
        mock_conversation_message_service = AsyncMock()
        mock_kx_dash_service = AsyncMock()
        conversation_service = ConversationService(
            conversation_repository=AsyncMock(),
            conversation_message_service=mock_conversation_message_service,
            extracted_data_service=AsyncMock(),
            document_service=AsyncMock(),
            kx_dash_service=mock_kx_dash_service,
        )
        conversation_id = UUID('00000000-0000-0000-0000-000000000000')

        mock_kx_dash_service.list.return_value = []

        mock_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=WELCOME_MESSAGE,
            options=(),
            suggested_prompts=[],
            system_reply_type=SystemReplyType.WELCOME_MESSAGE,
        )
        mock_conversation_message_service.create_message.return_value = mock_message

        result = await conversation_service._create_welcome_message(conversation_id, token='')

        mock_kx_dash_service.list.assert_awaited_once()
        mock_conversation_message_service.create_message.assert_awaited_once()

        assert result.content == WELCOME_MESSAGE
        assert result.suggested_prompts == []
