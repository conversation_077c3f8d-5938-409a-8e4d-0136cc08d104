"""Tests for the data complete conversation flow enhancement."""

from datetime import datetime
from unittest.mock import AsyncMock
from uuid import uuid4

import pytest

from constants.extracted_data import ConversationState, MissingDataStatus
from constants.message import CONFIRMED_FIELDS_READY, READY_TO_CREATE_DRAFT_QUAL, MessageRole, MessageType
from schemas import ConfirmedData, MissingDataResponse, UserMessageSerializer
from services.message_processor import ConversationMessageProcessor, ExtractionProcessingResult


class TestDataCompleteFlow:
    """Test the enhanced conversation flow for data complete state."""

    @pytest.fixture
    def test_conversation_id(self):
        """Generate a test conversation ID."""
        return uuid4()

    @pytest.fixture
    def mock_confirmed_data(self):
        """Create mock confirmed data with all fields filled."""
        return ConfirmedData(
            client_name='Test Client Corp',
            ldmf_country='United States',
            date_intervals=('2024-01-01', '2024-12-31'),
            objective_and_scope='Test objective and scope',
            outcomes='Test outcomes',
        )

    async def test_data_complete_shows_confirmed_fields_ready(self, test_conversation_id, mock_confirmed_data):
        """Test that when data is complete, CONFIRMED_FIELDS_READY message is shown."""
        user_m = UserMessageSerializer(
            conversation_id=test_conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content='I need help with my qual',
            created_at=datetime.now(),
            id=uuid4(),
            selected_option=None,
        )

        # Create processor with mocked dependencies
        processor = ConversationMessageProcessor(
            conversation_id=test_conversation_id,
            user_message=user_m,
            files=None,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            conversation_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
        )

        # Mock the conversation and confirmed data
        mock_conversation = type(
            'MockConversation', (), {'id': test_conversation_id, 'State': ConversationState.COLLECTING_OUTCOMES}
        )

        processor.conversation_repository.get = AsyncMock(return_value=mock_conversation)
        processor.conversation_repository.get_confirmed_data = AsyncMock(return_value=mock_confirmed_data)
        processor.conversation_repository.update_state = AsyncMock()

        # Mock the missing data response to indicate data is complete
        mock_missing_data_response = MissingDataResponse(
            status=MissingDataStatus.DATA_COMPLETE,
            message=None,
            next_expected_field=None,
            missing_fields=[],
            conversation_state=ConversationState.DATA_COMPLETE,
        )
        processor.extracted_data_service.get_missing_required_data_prompts = AsyncMock(
            return_value=mock_missing_data_response
        )

        # Execute the extract data processing
        result = await processor._extract_data()

        # Verify the result shows CONFIRMED_FIELDS_READY message
        assert isinstance(result, ExtractionProcessingResult)
        assert result.system_reply == CONFIRMED_FIELDS_READY
        assert result.missing_data_status == MissingDataStatus.DATA_COMPLETE

        # Verify conversation state was updated to DATA_COMPLETE
        processor.conversation_repository.update_state.assert_called_once_with(
            test_conversation_id, ConversationState.DATA_COMPLETE
        )

    async def test_user_confirms_ready_to_create_qual(self, test_conversation_id, mock_confirmed_data):
        """Test that when user confirms they're ready, READY_TO_CREATE_DRAFT_QUAL message is shown."""
        user_m = UserMessageSerializer(
            conversation_id=test_conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content='I need help with my qual',
            created_at=datetime.now(),
            id=uuid4(),
            selected_option=None,
        )
        # Create processor with user response indicating they're ready
        processor = ConversationMessageProcessor(
            conversation_id=test_conversation_id,
            user_message=user_m,
            files=None,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            conversation_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
        )
        # Test the data complete response handler directly
        processor.conversation_repository.update_state = AsyncMock()
        result = await processor._handle_data_complete_response('No, create my qual', mock_confirmed_data)

        # Verify the result shows formatted READY_TO_CREATE_DRAFT_QUAL message
        assert isinstance(result, ExtractionProcessingResult)
        expected_message = READY_TO_CREATE_DRAFT_QUAL.format(client_name='Test Client Corp')
        assert result.system_reply == expected_message
        assert result.missing_data_status == MissingDataStatus.DATA_COMPLETE

        # Verify conversation state was updated to READY_FOR_QUAL_CREATION
        processor.conversation_repository.update_state.assert_called_once_with(
            test_conversation_id, ConversationState.READY_FOR_QUAL_CREATION
        )

    async def test_user_wants_to_add_more_info(self, test_conversation_id, mock_confirmed_data):
        """Test that when user wants to add more info, they stay in DATA_COMPLETE state."""
        user_m = UserMessageSerializer(
            conversation_id=test_conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content='I need help with my qual',
            created_at=datetime.now(),
            id=uuid4(),
            selected_option=None,
        )
        # Create processor with user response indicating they want to add more
        processor = ConversationMessageProcessor(
            conversation_id=test_conversation_id,
            user_message=user_m,
            files=None,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            conversation_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
        )

        # Test the data complete response handler directly
        result = await processor._handle_data_complete_response(
            'Yes, I have more information to add', mock_confirmed_data
        )

        # Verify the result asks for additional information
        assert isinstance(result, ExtractionProcessingResult)
        assert result.system_reply == 'What additional information would you like to provide?'
        assert result.missing_data_status == MissingDataStatus.MISSING_DATA
        assert result.conversation_state == ConversationState.DATA_COMPLETE

    @pytest.mark.parametrize(
        'user_response', ['no', 'create my qual', 'generate the qual', 'proceed with creation', 'ready to create']
    )
    async def test_various_confirmation_responses(self, test_conversation_id, mock_confirmed_data, user_response):
        """Test that various user responses trigger qual creation flow."""
        user_m = UserMessageSerializer(
            conversation_id=test_conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content=user_response,
            created_at=datetime.now(),
            id=uuid4(),
            selected_option=None,
        )
        processor = ConversationMessageProcessor(
            conversation_id=test_conversation_id,
            user_message=user_m,
            files=None,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            conversation_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
        )

        result = await processor._handle_data_complete_response(user_response, mock_confirmed_data)

        # All should trigger qual creation flow
        assert isinstance(result, ExtractionProcessingResult)
        expected_message = READY_TO_CREATE_DRAFT_QUAL.format(client_name='Test Client Corp')
        assert result.system_reply == expected_message
        assert result.missing_data_status == MissingDataStatus.DATA_COMPLETE

    async def test_client_name_fallback_when_none(self, test_conversation_id):
        """Test that when client_name is None, fallback text is used."""

        # Create confirmed data without client name
        confirmed_data_no_client = ConfirmedData(
            client_name=None,
            ldmf_country='United States',
            date_intervals=('2024-01-01', '2024-12-31'),
            objective_and_scope='Test objective and scope',
            outcomes='Test outcomes',
        )

        user_m = UserMessageSerializer(
            conversation_id=test_conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content='No, create my qual',
            created_at=datetime.now(),
            id=uuid4(),
            selected_option=None,
        )

        processor = ConversationMessageProcessor(
            conversation_id=test_conversation_id,
            user_message=user_m,
            files=None,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            conversation_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
        )

        result = await processor._handle_data_complete_response('No, create my qual', confirmed_data_no_client)

        # Should use fallback client name
        expected_message = READY_TO_CREATE_DRAFT_QUAL.format(client_name='the client')
        assert result.system_reply == expected_message
