from unittest.mock import AsyncMock

from fastapi import FastAP<PERSON>, status
from httpx import ASGITransport
import pytest
from starlette.types import ASGIApp

from constants.operation_ids import operation_ids
from dependencies import CustomAsyncClient
from dependencies.services import get_kx_dash_service
from exceptions import EntityNotFoundError


@pytest.fixture
def mock_kx_dash_service(async_client: CustomAsyncClient):
    service = AsyncMock()
    transport = async_client._transport
    if isinstance(transport, ASGITransport):
        app: ASGIApp = transport.app
        if not isinstance(app, FastAPI):
            raise RuntimeError('Expected FastAPI app')
        original_factory = app.dependency_overrides.get(get_kx_dash_service)
        app.dependency_overrides[get_kx_dash_service] = lambda: service
        yield service
        if original_factory:
            app.dependency_overrides[get_kx_dash_service] = original_factory
        else:
            del app.dependency_overrides[get_kx_dash_service]
    else:
        raise RuntimeError('Expected ASGITransport')


async def test_list_activities_success(
    mock_kx_dash_service,
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    mock_activity,
):
    # Mock the service to return activities
    mock_kx_dash_service.list.return_value = [mock_activity]

    url = url_resolver.reverse(operation_ids.kx_dash.LIST)
    response = await async_client.get(url, headers=auth_header)

    assert response.status_code == status.HTTP_200_OK
    mock_kx_dash_service.list.assert_called_once()

    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 1

    activity = data[0]
    assert activity['activity_id'] == mock_activity['activityId']
    assert activity['activity_name'] == mock_activity['activityName']
    assert activity['client_name'] == mock_activity['clientName']
    assert activity['engagement_code'] == mock_activity['engagementCode']
    assert activity['due_date'] == mock_activity['dueDate']


async def test_list_activities_with_sorting_and_filtering(
    mock_kx_dash_service,
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
    kx_dash_repository,
):
    """
    Test that the list activities endpoint properly handles sorting by due date
    and filtering out used activity IDs.
    """
    mock_activities_response = list(kx_dash_repository.MOCKED_TASKS.values())
    mock_kx_dash_service.list.return_value = [
        mock_activities_response[3],  # 100004 - earliest due date (2024-04-15)
        mock_activities_response[2],  # 100003 - early due date (2024-05-01)
        mock_activities_response[1],  # 100002 - middle due date (2024-06-15)
        mock_activities_response[0],  # 100001 - latest due date (2024-12-30)
        mock_activities_response[4],  # 100005 - no due date (None) - sorted last
    ]

    url = url_resolver.reverse(operation_ids.kx_dash.LIST)
    response = await async_client.get(url, headers=auth_header)

    assert response.status_code == status.HTTP_200_OK
    mock_kx_dash_service.list.assert_called_once()

    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 5

    # Verify the activities are returned in the correct sorted order
    # Activities with due dates should come first, sorted by due date ascending
    # Activities with no due date should come last

    # First activity - earliest due date
    first_activity = data[0]
    assert first_activity['activity_id'] == 100004
    assert first_activity['activity_name'] == 'Tax Advisory - Earliest Due Date'
    assert first_activity['due_date'] == '2024-04-15'
    assert first_activity['client_name'] == 'Retail Chain Ltd'
    assert first_activity['engagement_code'] == 'ENG-100004'

    # Second activity - early due date
    second_activity = data[1]
    assert second_activity['activity_id'] == 100003
    assert second_activity['activity_name'] == 'Compliance Review - Should be Filtered'
    assert second_activity['due_date'] == '2024-05-01'
    assert second_activity['client_name'] == 'Manufacturing Corp'
    assert second_activity['engagement_code'] == 'ENG-100003'

    # Third activity - middle due date
    third_activity = data[2]
    assert third_activity['activity_id'] == 100002
    assert third_activity['activity_name'] == 'Risk Assessment - Middle Due Date'
    assert third_activity['due_date'] == '2024-06-15'
    assert third_activity['client_name'] == 'Global Financial Solutions Ltd'
    assert third_activity['engagement_code'] == 'ENG-100002'

    # Fourth activity - latest due date
    fourth_activity = data[3]
    assert fourth_activity['activity_id'] == 100001
    assert fourth_activity['activity_name'] == 'Quality Review - Late Due Date'
    assert fourth_activity['due_date'] == '2024-12-30'
    assert fourth_activity['client_name'] == 'TechCorp Industries'
    assert fourth_activity['engagement_code'] == 'ENG-100001'

    # Fifth activity - no due date (None)
    fifth_activity = data[4]
    assert fifth_activity['activity_id'] == 100005
    assert fifth_activity['activity_name'] == 'Strategy Consulting - No Due Date'
    assert fifth_activity['due_date'] is None
    assert fifth_activity['client_name'] == 'European Consulting Group'
    assert fifth_activity['engagement_code'] == 'ENG-100005'

    # Verify all activities have the expected structure and field mappings
    for activity in data:
        assert 'activity_id' in activity
        assert 'activity_name' in activity
        assert 'client_name' in activity
        assert 'member_firm' in activity
        assert 'country' in activity
        assert 'global_business' in activity
        assert 'engagement_code' in activity
        assert 'due_date' in activity  # Can be None
        assert 'engagement_start_date' in activity
        assert 'engagement_end_date' in activity


async def test_kx_dash_service_sorting_and_filtering_logic(
    kx_dash_service_with_repo,
    kx_dash_repository,
    extracted_data_service_mock,
):
    """
    Test the actual KXDashService.list method sorting and filtering logic
    by mocking the repository and extracted data service.
    """
    kx_dash_repository.list.return_value = list(kx_dash_repository.MOCKED_TASKS.values())

    # Call the service method
    result = await kx_dash_service_with_repo.list('test-token')

    # Verify repository was called
    kx_dash_repository.list.assert_called_once_with('test-token')

    # Verify the result is correctly sorted and filtered
    # Only activities with status 'Not Started' and activity type 'Contribute qual' should be returned (100003, 100004, 100005)
    # Activities 100001 and 100002 have status 'In Progress' and activity type 'Contribute engagement' and should be filtered out
    assert len(result) == 3

    # Verify sorting: activities with due dates first (ascending), then None values
    assert result[0].activity_id == 100004  # Earliest due date (2024-04-15)
    assert result[0].due_date.isoformat() == '2024-04-15'

    assert result[1].activity_id == 100003  # Middle due date (2024-05-01)
    assert result[1].due_date.isoformat() == '2024-05-01'

    assert result[2].activity_id == 100005  # No due date (None)
    assert result[2].due_date is None

    # Verify the filtered activities (100001, 100002 with 'In Progress' status) are not in the result
    activity_ids_in_result = {activity.activity_id for activity in result}
    assert 100001 not in activity_ids_in_result
    assert 100002 not in activity_ids_in_result


async def test_list_activities_empty_result(
    mock_kx_dash_service,
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    url_resolver,
):
    """Test that empty results are handled correctly."""
    # Mock the service to return empty list
    mock_kx_dash_service.list.return_value = []

    url = url_resolver.reverse(operation_ids.kx_dash.LIST)
    response = await async_client.get(url, headers=auth_header)

    assert response.status_code == status.HTTP_200_OK
    mock_kx_dash_service.list.assert_called_once()

    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 0


async def test_list_activities_missing_query(
    mock_kx_dash_service, auth_mock, auth_header, async_client: CustomAsyncClient, url_resolver
):
    # Mock service to raise an error when no activities are found
    mock_kx_dash_service.list.side_effect = EntityNotFoundError('Activities', 'No activities found')

    url = url_resolver.reverse(operation_ids.kx_dash.LIST)
    response = await async_client.get(url, headers=auth_header)

    mock_kx_dash_service.list.assert_called_once()
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {'detail': 'Failed to list activities'}


async def test_list_activities_server_error(
    mock_kx_dash_service, auth_mock, auth_header, async_client: CustomAsyncClient, url_resolver
):
    # Mock service to raise an error
    mock_kx_dash_service.list.side_effect = Exception('Server error')

    url = url_resolver.reverse(operation_ids.kx_dash.LIST)
    response = await async_client.get(url, headers=auth_header)

    mock_kx_dash_service.list.assert_called_once()
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {'detail': 'Failed to list activities'}


async def test_get_activity_success(
    mock_kx_dash_service, auth_mock, auth_header, async_client: CustomAsyncClient, url_resolver, mock_activity
):
    # Mock service to return activity
    mock_kx_dash_service.get.return_value = mock_activity

    activity_id = 100001
    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id=activity_id)
    response = await async_client.get(url, headers=auth_header)

    mock_kx_dash_service.get.assert_called_once_with(activity_id, 'SOME_TEST_TOKEN')
    assert response.status_code == status.HTTP_200_OK

    data = response.json()
    assert data['activity_id'] == mock_activity['activityId']
    assert data['activity_name'] == mock_activity['activityName']
    assert data['client_name'] == mock_activity['clientName']
    assert data['engagement_code'] == mock_activity['engagementCode']
    assert data['member_firm'] == mock_activity['memberFirm']
    assert data['country'] == mock_activity['country']
    assert data['due_date'] == mock_activity['dueDate']


async def test_get_activity_not_found(
    mock_kx_dash_service, auth_mock, auth_header, async_client: CustomAsyncClient, url_resolver
):
    activity_id = 999999
    # Mock service to raise EntityNotFoundError
    mock_kx_dash_service.get.side_effect = EntityNotFoundError('Activity', str(activity_id))

    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id=activity_id)
    response = await async_client.get(url, headers=auth_header)

    mock_kx_dash_service.get.assert_called_once_with(activity_id, 'SOME_TEST_TOKEN')
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert 'not found' in response.json()['detail']


async def test_get_activity_server_error(
    mock_kx_dash_service, auth_mock, auth_header, async_client: CustomAsyncClient, url_resolver
):
    activity_id = 100001
    # Mock service to raise an error
    mock_kx_dash_service.get.side_effect = Exception('Server error')

    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id=activity_id)
    response = await async_client.get(url, headers=auth_header)

    mock_kx_dash_service.get.assert_called_once_with(activity_id, 'SOME_TEST_TOKEN')
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {'detail': 'Failed to retrieve activity'}


async def test_get_activity_invalid_id(auth_mock, auth_header, async_client: CustomAsyncClient, url_resolver):
    url = url_resolver.reverse(operation_ids.kx_dash.GET, activity_id='not-an-integer')

    response = await async_client.get(url, headers=auth_header)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
