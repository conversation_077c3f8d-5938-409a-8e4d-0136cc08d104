from core.enum import StrEnum


__all__ = [
    'DataSourceType',
    'RequiredField',
    'ConversationState',
    'FieldStatus',
    'MissingDataStatus',
    'ConfirmedDataFields',
    'FieldCompletionStatus',
    'ProgressStatus',
]


class DataSourceType(StrEnum):
    KX_DASH = 'kx_dash'
    DOCUMENTS = 'documents'
    PROMPT = 'prompt'


class RequiredField(StrEnum):
    """Enum representing required fields for a qual."""

    CLIENT_INFO = 'client_info'
    LDMF_COUNTRY = 'ldmf_country'
    ENGAGEMENT_DATES = 'engagement_dates'
    OBJECTIVE_SCOPE = 'objective_scope'
    OUTCOMES = 'outcomes'


class ConversationState(StrEnum):
    """Enum representing conversation states during qual data collection."""

    INITIAL = 'initial'
    COLLECTING_CLIENT_NAME = 'collecting_client_name'
    COLLECTING_COUNTRY = 'collecting_country'
    COLLECTING_DATES = 'collecting_dates'
    COLLECTING_OBJECTIVE = 'collecting_objective'
    COLLECTING_OUTCOMES = 'collecting_outcomes'
    COLLECTING_ADDITIONAL_DATA = 'collecting_additional_data'
    DATA_COMPLETE = 'data_complete'
    READY_FOR_QUAL_CREATION = 'ready_for_qual_creation'
    QUAL_CREATED = 'qual_created'


class FieldStatus(StrEnum):
    """Enum representing the status of a field during qual data collection."""

    MISSING = 'missing'
    SINGLE = 'single'
    MULTIPLE = 'multiple'
    CONFIRMED = 'confirmed'
    PENDING_CONFIRMATION = 'pending_confirmation'


class MissingDataStatus(StrEnum):
    """Enum representing the status of missing data during qual data collection."""

    MISSING_DATA = 'missing_data'
    DATA_COMPLETE = 'data_complete'
    ERROR = 'error'


class ConfirmedDataFields(StrEnum):
    CLIENT_NAME = 'client_name'
    LDMF_COUNTRY = 'ldmf_country'
    DATE_INTERVALS = 'date_intervals'
    OBJECTIVE_AND_SCOPE = 'objective_and_scope'
    OUTCOMES = 'outcomes'
    PROPOSED_CLIENT_NAME = 'proposed_client_name'


class FieldCompletionStatus(StrEnum):
    """Enum representing the completion status of a field."""

    MISSING = 'missing'
    PENDING_CONFIRMATION = 'pending_confirmation'
    COMPLETED = 'completed'


class ProgressStatus(StrEnum):
    """Enum representing the overall progress status."""

    INITIAL = 'initial'
    IN_PROGRESS = 'in_progress'
    COMPLETED = 'completed'
