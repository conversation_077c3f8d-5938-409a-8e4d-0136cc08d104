import logging
from typing import Any, Dict, List
import uuid

from langchain_text_splitters import TokenTextSplitter

from durable_functions.application.config import settings

from .base import BaseChunkingStrategy


logger = logging.getLogger(__name__)


class TokenTextSplitterStrategy(BaseChunkingStrategy):
    """Chunking strategy using LangChain's TokenTextSplitter."""

    def __init__(
        self,
        chunk_size: int = settings.CHUNKING_SETTINGS.CHUNK_SIZE,
        chunk_overlap: int = settings.CHUNKING_SETTINGS.CHUNK_OVERLAP,
        encoding_name: str = settings.CHUNKING_SETTINGS.ENCODING_NAME,
    ):
        """
        Initialize the TokenTextSplitter strategy.

        Args:
            chunk_size: Maximum size of each chunk in tokens
            chunk_overlap: Number of tokens to overlap between chunks
            encoding_name: Name of the tiktoken encoding to use
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.encoding_name = encoding_name

        # Create the token text splitter
        self.text_splitter = TokenTextSplitter(
            encoding_name=encoding_name,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
        )

    def chunk_document(self, document_content: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Chunk a document using LangChain's TokenTextSplitter.

        Args:
            document_content: The text content of the document
            metadata: Additional information about the document

        Returns:
            List of chunks, each containing the text and metadata
        """
        logger.info(f'Chunking document using TokenTextSplitter with chunk size {self.chunk_size}')

        # Split the text into chunks
        text_chunks = self.text_splitter.split_text(document_content)

        # Create chunk dictionaries
        chunks = []
        for i, chunk_text in enumerate(text_chunks):
            chunks.append(self._create_chunk(chunk_text, metadata, i))

        return chunks

    def _get_token_count(self, text: str) -> int:
        """Get the approximate number of tokens in the text."""
        try:
            # Use the text splitter's built-in token counter
            return self.text_splitter._length_function(text)
        except Exception as e:
            logger.warning(f'Failed to count tokens: {e}')
            # Fallback to approximate token count
            return len(text) // 4  # Rough estimate: ~4 chars per token

    def _create_chunk(self, text: str, metadata: Dict[str, Any], index: int) -> Dict[str, Any]:
        """Create a chunk dictionary with text and metadata."""
        return {
            'chunk_id': str(uuid.uuid4()),
            'chunk_index': index,
            'text': text.strip(),
            'metadata': metadata,
            'chunking_strategy': 'token',
            'token_count': self._get_token_count(text),
        }
