import json
import logging

import azure.durable_functions as df
from azure.functions import QueueMessage

from durable_functions.application.config import settings
from durable_functions.utils import OrchestratorInputType, OrchestratorName


logger = logging.getLogger(__name__)

CONTENT_PROCESSING_QUEUE_NAME = settings.QUEUE_SETTINGS.CONTENT_PROCESSING_QUEUE_NAME
CONNECTION_ENV = settings.QUEUE_SETTINGS.CONNECTION_ENV

bp = df.Blueprint()


@bp.queue_trigger(arg_name='msg', queue_name=CONTENT_PROCESSING_QUEUE_NAME, connection=CONNECTION_ENV)
@bp.durable_client_input(client_name='client')
async def process_unified_queue(msg: QueueMessage, client: df.DurableOrchestrationClient) -> None:
    """
    Unified queue trigger to start document/prompt processing.

    Expected message format (QualQueueMessage):
    {
        "source": {
            "text_prompt": "https://..." (optional),
            "documents": ["https://...", "https://..."] (optional)
        },
        "signal_r_connection_id": "user-uuid"
    }

    Legacy message formats are also supported:
    - {"blob_url": "https://...", "signalr_user_id": "user-uuid"}
    - {"prompt_url": "https://...", "signalr_user_id": "user-uuid"}

    Args:
        msg: Queue message
        client: Durable orchestration client

    Returns:
        None
    """
    try:
        msg_body = msg.get_body().decode('utf-8')
        msg_json = json.loads(msg_body)
        logger.info(f'Received unified queue message: {msg_json}')

        # Handle unified QualQueueMessage format
        if 'source' in msg_json:
            source = msg_json['source']
            signalr_user_id = msg_json['signal_r_connection_id']

            text_prompt = source.get('text_prompt')
            documents = source.get('documents')

            # Check if we have both text and documents (unified processing)
            if text_prompt and documents:
                # Use unified orchestrator for parallel processing
                orchestrator_input = {
                    'text_prompt': text_prompt,
                    'documents': documents,
                    'signalr_user_id': signalr_user_id,
                }
                orchestrator_name = OrchestratorName.UnifiedProcessing

                logger.info(f'Starting unified orchestrator: {orchestrator_name} with input: {orchestrator_input}')
                instance_id = await client.start_new(orchestrator_name, None, orchestrator_input)
                logger.info(f'Started unified orchestration instance: {instance_id}')

            else:
                # Process individual sources using existing logic

                # Process documents if present
                if documents:
                    for document_url in documents:
                        orchestrator_input = {
                            'blob_url': document_url,
                            'type': OrchestratorInputType.Document,
                            'signalr_user_id': signalr_user_id,
                        }
                        orchestrator_name = OrchestratorName.DocumentProcessing

                        logger.info(
                            f'Starting document orchestrator: {orchestrator_name} with input: {orchestrator_input}'
                        )
                        instance_id = await client.start_new(orchestrator_name, None, orchestrator_input)
                        logger.info(f'Started document orchestration instance: {instance_id}')

                # Process text prompt if present
                if text_prompt:
                    orchestrator_input = {
                        'prompt_url': text_prompt,
                        'type': OrchestratorInputType.Prompt,
                        'signalr_user_id': signalr_user_id,
                    }
                    orchestrator_name = OrchestratorName.DocumentProcessing

                    logger.info(f'Starting prompt orchestrator: {orchestrator_name} with input: {orchestrator_input}')
                    instance_id = await client.start_new(orchestrator_name, None, orchestrator_input)
                    logger.info(f'Started prompt orchestration instance: {instance_id}')

        else:
            logger.error(f'Unknown message format: {msg_json}')

    except Exception as e:
        logger.exception(f'Error processing unified queue message: {e}')
