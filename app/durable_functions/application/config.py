from functools import cached_property
import logging
import os

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings

from constants.environment import Environment


logger = logging.getLogger(__name__)


ENVIRONMENT = Environment(os.environ['ENVIRONMENT'])


class SignalRSettings(BaseModel):
    CONNECTION_STRING: str
    HUB_NAME: str = 'qualMessageHub'


class BlobStorageSettings(BaseModel):
    CONNECTION_STRING: str
    CONTAINER_NAME: str = 'documents'


class QueueSettings(BaseModel):
    CONNECTION_STRING: str

    CONTENT_PROCESSING_QUEUE_NAME: str = 'content-analysis-queue'
    CONTENT_PROCESSING_QUEUE_CHUNKED: str = 'content-analysis-chunked'
    CONTENT_PROCESSING_QUEUE_EXTRACTED: str = 'content-analysis-extracted'

    CONNECTION_ENV: str = 'AZURE_QUEUE_CONNECTION_STRING'


class DocumentIntelligenceSettings(BaseModel):
    ENDPOINT: str
    KEY: str
    MODEL_NAME: str = 'prebuilt-layout'


class ChunkingSettings(BaseModel):
    CHUNK_SIZE: int = 8096
    CHUNK_OVERLAP: int = 512
    ENCODING_NAME: str = 'cl100k_base'
    SEPARATORS: list[str] = ['\n\n', '\n', '. ', ' ', '']


class DatabaseSettings(BaseModel):
    host: str
    port: str
    user: str
    password: str
    name: str
    driver: str

    @cached_property
    def uri(self) -> str:
        return (
            f'mssql+aioodbc:///?odbc_connect=DRIVER={{{self.driver}}};SERVER={self.host},{self.port};'
            f'DATABASE={self.name};UID={self.user};PWD={self.password};'
        )


class OpenAISettings(BaseModel):
    """Settings for Azure OpenAI API."""

    endpoint: str
    key: str
    model: str
    api_version: str
    default_temperature: float = 0.0


class Settings(BaseSettings):
    SIGNALR_SETTINGS: SignalRSettings
    BLOB_STORAGE_SETTINGS: BlobStorageSettings
    QUEUE_SETTINGS: QueueSettings
    DOCUMENT_INTELLIGENCE_SETTINGS: DocumentIntelligenceSettings
    CHUNKING_SETTINGS: ChunkingSettings = Field(default=ChunkingSettings())
    db: DatabaseSettings
    openai: OpenAISettings

    class Config:
        env_file = f'{ENVIRONMENT.value}.env'
        env_nested_delimiter = '__'
        extra = 'allow'  # Allow extra fields from environment variables


logger.info(f'Loading settings from {ENVIRONMENT.value}.env')

settings = Settings(
    db=DatabaseSettings(
        host=os.environ['DB_HOST'],
        port=os.environ['DB_PORT'],
        user=os.environ['DB_USER'],
        password=os.environ['DB_PASSWORD'],
        name=('test_' if ENVIRONMENT == Environment.TEST else '') + os.environ['DB_NAME'],
        driver=os.environ.get('DB_DRIVER', 'ODBC+Driver+17+for+SQL+Server'),
    ),
    openai=OpenAISettings(
        endpoint=os.environ['AZURE_OPENAI_ENDPOINT'],
        key=os.environ['AZURE_OPENAI_KEY'],
        model=os.environ['AZURE_OPENAI_MODEL'],
        api_version=os.environ['AZURE_OPENAI_API_VERSION'],
        default_temperature=float(os.environ.get('AZURE_OPENAI_DEFAULT_TEMPERATURE', 0.0)),
    ),
)  # type: ignore
