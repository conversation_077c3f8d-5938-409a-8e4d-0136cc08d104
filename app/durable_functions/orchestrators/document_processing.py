import logging

import azure.durable_functions as df

from constants.extracted_data import DataSourceType
from durable_functions.activities.models import (
    ChunkDocumentActivityInput,
    ChunkDocumentActivityOutput,
    ExtractDataActivityInput,
    ExtractDocumentTextActivityInput,
    ExtractDocumentTextActivityOutput,
    ExtractDocumentTextActivityOutputFailed,
    ReadPromptActivityInput,
    SaveExtractionDataActivityInput,
    SendNotificationActivityInput,
    SendQueueMessageActivityInput,
    UpdateProcessingStatusActivityInput,
)
from durable_functions.utils import (
    ActivityName,
    EventType,
    ExctractStatus,
    OrchestratorInputType,
    OrchestratorName,
    ProcessingStatus,
    parse_blob_url,
)
from durable_functions.utils.models import FinalExtractionDataResults, LLMExtractedDataResult

from .models import (
    DocumentProcessingOrchestratorOutput,
    DocumentProcessingOrchestratorOutputFailed,
    ProcessDocumentInput,
    ProcessPromptInput,
)


logger = logging.getLogger(__name__)
bp = df.Blueprint()


def process_document_logic(context: df.DurableOrchestrationContext, input_data: ProcessDocumentInput, logger):
    blob_url = input_data.blob_url
    signalr_user_id = input_data.signalr_user_id
    message_id, file_name = parse_blob_url(blob_url)

    logger.info(f'Starting document processing for {file_name} with message ID {message_id}')

    # Update status to "processing"
    yield context.call_activity(
        ActivityName.UpdateProcessingStatus,
        UpdateProcessingStatusActivityInput(
            message_id=message_id,
            status=ProcessingStatus.DocumentExtractionStarted,
            message=f'Processing document: {file_name}',
        ),
    )

    # Send notification
    yield context.call_activity(
        ActivityName.SendNotification,
        SendNotificationActivityInput(
            event_type=EventType.DocumentExtractionStarted,
            data={'message_id': message_id, 'file_name': file_name},
            signalr_user_id=signalr_user_id,
        ),
    )

    retry_options = df.RetryOptions(first_retry_interval_in_milliseconds=5000, max_number_of_attempts=3)
    # Step 1: Extract text from the document with retries
    extraction_result: (
        ExtractDocumentTextActivityOutput | ExtractDocumentTextActivityOutputFailed
    ) = yield context.call_activity_with_retry(
        ActivityName.ExtractDocumentText,
        retry_options,
        ExtractDocumentTextActivityInput(
            blob_url=blob_url,
            message_id=message_id,
            file_name=file_name,
        ),
    )

    # Check if extraction failed
    if extraction_result.status == ExctractStatus.Failed:
        failed_result = (
            extraction_result if isinstance(extraction_result, ExtractDocumentTextActivityOutputFailed) else None
        )
        error_message = failed_result.error if failed_result else 'Unknown extraction error'

        # Update status to "failed"
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.DocumentExtractionFailed,
                message=f'Document processing failed: {error_message}',
            ),
        )

        if failed_result and failed_result.file_is_corrupted:
            event_type = EventType.DocumentIsCorruptedError
        else:
            event_type = EventType.DocumentExtractionFailed

        logger.info(f'Sending notification for failed document processing: {event_type}')

        # Send notification
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=event_type,
                data={'message_id': message_id, 'file_name': file_name, 'error': error_message},
                signalr_user_id=signalr_user_id,
            ),
        )
        response = DocumentProcessingOrchestratorOutputFailed.model_validate(
            {
                'message_id': message_id,
                'file_name': file_name,
                'status': ProcessingStatus.OrchestratorDocumentProcessingFailed,
                'error': error_message,
            }
        ).model_dump()
        return response

    # At this point, we know extraction was successful, so we can safely cast
    success_result = extraction_result if isinstance(extraction_result, ExtractDocumentTextActivityOutput) else None

    if not success_result:
        raise ValueError('Extraction result has success status but is not of success type')

    # Update status after extraction
    yield context.call_activity(
        ActivityName.UpdateProcessingStatus,
        UpdateProcessingStatusActivityInput(
            message_id=message_id,
            status=ProcessingStatus.DocumentExtractionCompleted,
            message=f'Text extracted from document: {file_name}',
            metadata={'extraction_url': success_result.extraction_url},
        ),
    )

    # Step 2: Chunk the document
    chunking_result: ChunkDocumentActivityOutput = yield context.call_activity(
        ActivityName.ChunkDocument,
        ChunkDocumentActivityInput(
            message_id=message_id,
            file_name=file_name,
            extraction_url=success_result.extraction_url,
            text_content=success_result.text_content,
            metadata=success_result.metadata,
        ),
    )

    yield context.call_activity(
        ActivityName.UpdateProcessingStatus,
        UpdateProcessingStatusActivityInput(
            message_id=message_id,
            status=ProcessingStatus.DocumentChunkingCompleted,
            message=f'Document processing completed: {file_name}',
            metadata={
                'chunk_count': chunking_result.chunk_count,
                'chunk_urls': chunking_result.chunk_urls,
            },
        ),
    )

    yield context.call_activity(
        ActivityName.SendNotification,
        SendNotificationActivityInput(
            event_type=EventType.DocumentChunkingCompleted,
            data={'message_id': message_id, 'file_name': file_name, 'chunk_count': chunking_result.chunk_count},
        ),
    )
    response = DocumentProcessingOrchestratorOutput.model_validate(
        {
            'message_id': message_id,
            'file_name': file_name,
            'status': ProcessingStatus.OrchestratorDocumentProcessingCompleted,
            'extraction_url': success_result.extraction_url,
            'chunk_count': chunking_result.chunk_count,
            'chunk_urls': chunking_result.chunk_urls,
            'signalr_user_id': signalr_user_id,
        }
    )

    logger.info(f'Chunking result: {chunking_result}')
    # Step 3: Extract data from each chunk.
    retry_options = df.RetryOptions(first_retry_interval_in_milliseconds=5000, max_number_of_attempts=3)
    tasks = []
    for chunk in chunking_result.chunk_urls:
        tasks.append(
            context.call_activity_with_retry(
                ActivityName.ExtractData,
                retry_options,
                ExtractDataActivityInput(chunk_url=chunk['url']),
            )
        )
    extract_data_results: list[LLMExtractedDataResult] = yield context.task_all(tasks)

    # merge extracted results
    final_extraction_data_results: FinalExtractionDataResults = yield context.call_activity(
        ActivityName.MergeExtractionData,
        extract_data_results,
    )
    logger.info(f'Final extraction data results: {final_extraction_data_results}')
    response.metadata = final_extraction_data_results.model_dump()
    response = response.model_dump()
    # Step 4: Save the final extraction data results
    yield context.call_activity(
        ActivityName.SaveExtractionData,
        SaveExtractionDataActivityInput(
            message_id=message_id,
            extraction_data=final_extraction_data_results,
            data_source_type=DataSourceType.DOCUMENTS,
        ),
    )

    yield context.call_activity(
        ActivityName.SendQueueMessage,
        SendQueueMessageActivityInput(
            message_id=message_id,
            file_name=file_name,
            chunk_count=chunking_result.chunk_count,
            chunk_urls=chunking_result.chunk_urls,
            signalr_user_id=signalr_user_id,
        ),
    )

    # Only send RequiredFieldsExtracted event if not part of unified processing
    if not input_data.is_part_of_unified_processing:
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.DocumentRequiredFieldsExtracted,
                message=f'Processing finished: {input_data.blob_url}',
                metadata={'extraction_url': input_data.blob_url, 'results': final_extraction_data_results.model_dump()},
            ),
        )

        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.RequiredFieldsExtracted,
                data={
                    'message_id': message_id,
                    'prompt_url': input_data.blob_url,
                    'results': final_extraction_data_results.model_dump(),
                },
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

    return response


def process_prompt_logic(context: df.DurableOrchestrationContext, input_data: ProcessPromptInput, logger):
    logger.info(f'Starting prompt processing for {input_data.prompt_url}')
    message_id, file_name = parse_blob_url(input_data.prompt_url, is_prompt=True)

    # 1. Update status and send notification that prompt processing is started
    yield context.call_activity(
        ActivityName.UpdateProcessingStatus,
        UpdateProcessingStatusActivityInput(
            message_id=message_id,
            status=ProcessingStatus.PromptProcessingStarted,
            message=f'Processing prompt: {input_data.prompt_url}',
        ),
    )

    yield context.call_activity(
        ActivityName.SendNotification,
        SendNotificationActivityInput(
            event_type=EventType.PromptProcessingStarted,
            data={'message_id': message_id, 'prompt_url': input_data.prompt_url},
            signalr_user_id=input_data.signalr_user_id,
        ),
    )

    # Step 1.5: Read text from the prompt
    prompt_text: str = yield context.call_activity(
        ActivityName.ReadPrompt,
        ReadPromptActivityInput(
            prompt_url=input_data.prompt_url,
        ),
    )

    # Step 2: Chunk the prompt
    chunked_prompt: ChunkDocumentActivityOutput = yield context.call_activity(
        ActivityName.ChunkDocument,
        ChunkDocumentActivityInput(
            message_id=message_id,
            file_name=file_name,
            extraction_url=input_data.prompt_url,
            text_content=prompt_text,
            metadata={'original_prompt_url': input_data.prompt_url},
        ),
    )

    # 3. Update status and send notification that prompt chunking is completed
    yield context.call_activity(
        ActivityName.UpdateProcessingStatus,
        UpdateProcessingStatusActivityInput(
            message_id=message_id,
            status=ProcessingStatus.PromptChunkingCompleted,
            message=f'Prompt chunking completed for: {input_data.prompt_url}',
            metadata={'chunk_count': chunked_prompt.chunk_count},
        ),
    )

    # Step 3: Extract data from each chunk.
    retry_options = df.RetryOptions(first_retry_interval_in_milliseconds=5000, max_number_of_attempts=3)
    tasks = []
    for chunk in chunked_prompt.chunk_urls:
        tasks.append(
            context.call_activity_with_retry(
                ActivityName.ExtractData,
                retry_options,
                ExtractDataActivityInput(chunk_url=chunk['url']),
            )
        )
    extract_data_results: list[LLMExtractedDataResult] = yield context.task_all(tasks)

    # merge extracted results
    final_extraction_data_results: FinalExtractionDataResults = yield context.call_activity(
        ActivityName.MergeExtractionData,
        extract_data_results,
    )
    logger.info(f'Final extraction data results: {final_extraction_data_results}')

    # Step 4: Save the final extraction data results
    yield context.call_activity(
        ActivityName.SaveExtractionData,
        SaveExtractionDataActivityInput(
            message_id=message_id,
            extraction_data=final_extraction_data_results,
            data_source_type=DataSourceType.PROMPT,
        ),
    )

    yield context.call_activity(
        ActivityName.SendNotification,
        SendNotificationActivityInput(
            event_type=EventType.PromptChunkingCompleted,
            data={
                'message_id': message_id,
                'prompt_url': input_data.prompt_url,
                'chunk_count': chunked_prompt.chunk_count,
                'chunk_urls': chunked_prompt.chunk_urls,
            },
            signalr_user_id=input_data.signalr_user_id,
        ),
    )

    response = DocumentProcessingOrchestratorOutput.model_validate(
        {
            'message_id': message_id,
            'file_name': file_name,
            'status': ProcessingStatus.OrchestratorDocumentProcessingCompleted,
            'extraction_url': input_data.prompt_url,
            'chunk_count': chunked_prompt.chunk_count,
            'chunk_urls': chunked_prompt.chunk_urls,
            'signalr_user_id': input_data.signalr_user_id,
            'metadata': final_extraction_data_results.model_dump(),
        }
    ).model_dump()

    yield context.call_activity(
        ActivityName.SendQueueMessage,
        SendQueueMessageActivityInput(
            message_id=message_id,
            file_name=file_name,
            chunk_count=chunked_prompt.chunk_count,
            chunk_urls=chunked_prompt.chunk_urls,
            signalr_user_id=input_data.signalr_user_id,
            input_type=OrchestratorInputType.Prompt,
        ),
    )

    # Only send RequiredFieldsExtracted event if not part of unified processing
    if not input_data.is_part_of_unified_processing:
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.PromptRequiredFieldsExtracted,
                message=f'Processing finished: {input_data.prompt_url}',
                metadata={
                    'extraction_url': input_data.prompt_url,
                    'results': final_extraction_data_results.model_dump(),
                },
            ),
        )

        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.RequiredFieldsExtracted,
                data={
                    'message_id': message_id,
                    'prompt_url': input_data.prompt_url,
                    'results': final_extraction_data_results.model_dump(),
                },
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

    return response


@bp.orchestration_trigger('context', OrchestratorName.DocumentProcessing)
def document_processing_orchestrator(context: df.DurableOrchestrationContext):
    """
    Orchestrator function for document processing.

    This orchestrator coordinates the document processing workflow:
    1. Extract text from the document using Document Intelligence
    2. Save the extraction result to blob storage
    3. Chunk the document
    4. Save the chunks to blob storage
    5. Update the processing status

    Args:
        context: Durable orchestration context

    Returns:
        Dictionary containing the processing results
    """

    message_id, file_name, signalr_user_id = None, None, None
    input_dict = context.get_input()
    processing_type = input_dict.get('type')  # type: ignore

    if processing_type == OrchestratorInputType.Document:
        blob_url = input_dict.get('blob_url')  # type: ignore
        message_id, file_name = parse_blob_url(blob_url)

        try:
            input_data = ProcessDocumentInput.model_validate(input_dict)
            response = yield from process_document_logic(context, input_data, logger)
            return response

        except Exception as e:
            logger.exception('Error in document processing orchestrator')

            # Handle the case where message_id might be None
            safe_message_id = message_id if message_id else 'unknown'
            safe_file_name = file_name if file_name else 'unknown'

            # Only try to send status updates if we have a message ID
            if message_id:
                try:
                    yield context.call_activity(
                        ActivityName.UpdateProcessingStatus,
                        UpdateProcessingStatusActivityInput(
                            message_id=safe_message_id,
                            status=ProcessingStatus.DocumentProcessingFailed,
                            message=f'Document processing failed: {str(e)}',
                        ),
                    )

                    yield context.call_activity(
                        ActivityName.SendNotification,
                        SendNotificationActivityInput(
                            event_type=EventType.DocumentProcessingFailed,
                            data={'message_id': safe_message_id, 'file_name': safe_file_name, 'error': str(e)},
                            signalr_user_id=signalr_user_id,
                        ),
                    )
                except Exception:
                    # Log but don't re-raise to ensure we still raise the original exception
                    logger.exception('Error sending failure notifications')

            # Re-raise the original exception
            raise

    elif processing_type == OrchestratorInputType.Prompt:
        input_data = ProcessPromptInput.model_validate(input_dict)
        response = yield from process_prompt_logic(context, input_data, logger)
        return response

    else:
        logging.warning(f'Unknown processing type: {processing_type}')
        result = {'status': 'Failed', 'message': f'Unknown processing type: {processing_type}'}

        return result
