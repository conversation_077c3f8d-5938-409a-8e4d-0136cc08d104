import logging

from openai import AsyncAzureOpenAI

from constants.message import EXTRACT_DATA_SYSTEM_PROMPT, EXTRACT_DATA_USER_PROMPT
from durable_functions.application.config import settings
from durable_functions.utils.models import LLMExtractedDataResult


__all__ = ['OpenAIRepository']


logger = logging.getLogger(__name__)

openai_client = AsyncAzureOpenAI(
    api_key=settings.openai.key,
    api_version=settings.openai.api_version,
    azure_endpoint=settings.openai.endpoint,
)


class OpenAIRepository:
    """Repository for interacting with Azure OpenAI API."""

    def __init__(self, client: AsyncAzureOpenAI = openai_client) -> None:
        """Initialize the OpenAI service with settings from the config."""
        self.client = client

    async def extract_data(
        self,
        text: str,
        temperature: float = 0.0,
        model: str = settings.openai.model,
    ) -> LLMExtractedDataResult:
        try:
            completion = await self.client.beta.chat.completions.parse(
                model=model,
                messages=[
                    {'role': 'system', 'content': EXTRACT_DATA_SYSTEM_PROMPT},
                    {'role': 'user', 'content': EXTRACT_DATA_USER_PROMPT.format(text=text)},
                ],
                response_format=LLMExtractedDataResult,
                temperature=temperature,
            )
            message = completion.choices[0].message
            if message.refusal:
                logger.warning(f'OpenAI refused to extract data: {text}')
                return LLMExtractedDataResult()
            logger.info(f'Extracted data in AI: {message.parsed}')
            return message.parsed  # type: ignore
        except Exception:
            logger.warning('Error extracting data')
            return LLMExtractedDataResult()
