from typing import Annotated

from fastapi import Depends

from services import (
    AuthService,
    ConversationMessageService,
    ConversationService,
    DateValidatorService,
    DocumentService,
    ExtractedDataService,
    IndustryDataService,
    IntentClassifierService,
    KXDashService,
    LDMFCountryService,
    RoleDataService,
    ServiceDataService,
    SystemMessageGenerationService,
)

from .repositories import (
    ConversationMessageRepositoryDep,
    ConversationRepositoryDep,
    DocumentBlobRepositoryDep,
    DocumentDbRepositoryDep,
    DocumentQueueRepositoryDep,
    ExtractedDataRepositoryDep,
    IndustryRepositoryDep,
    KXDashRepositoryDep,
    LDMFCountriesRepositoryDep,
    OpenAIRepositoryDep,
    QualsClientsRepositoryDep,
    RoleRepositoryDep,
    ServiceRepositoryDep,
)


__all__ = [
    'AuthServiceDep',
    'IntentClassifierServiceDep',
    'ExtractedDataServiceDep',
    'KXDashServiceDep',
    'DocumentServiceDep',
    'ConversationMessageServiceDep',
    'ConversationServiceDep',
    'SystemMessageGenerationServiceDep',
]


def get_auth_service() -> AuthService:
    return AuthService()


AuthServiceDep = Annotated[AuthService, Depends(get_auth_service)]


def get_intent_classifier_service(openai_service: OpenAIRepositoryDep) -> IntentClassifierService:
    """Get the intent classifier for dependency injection."""
    return IntentClassifierService(openai_service=openai_service)


IntentClassifierServiceDep = Annotated[IntentClassifierService, Depends(get_intent_classifier_service)]


def get_industry_data_service(
    industry_repository: IndustryRepositoryDep,
) -> IndustryDataService:
    return IndustryDataService(industry_repository=industry_repository)


IndustryDataServiceDep = Annotated[IndustryDataService, Depends(get_industry_data_service)]


def get_role_data_service(
    role_repository: RoleRepositoryDep,
) -> RoleDataService:
    return RoleDataService(role_repository=role_repository)


RoleDataServiceDep = Annotated[RoleDataService, Depends(get_role_data_service)]


def get_service_data_service(
    service_repository: ServiceRepositoryDep,
) -> ServiceDataService:
    return ServiceDataService(service_repository=service_repository)


ServiceDataServiceDep = Annotated[ServiceDataService, Depends(get_service_data_service)]


def get_ldmf_country_service(
    ldmf_countries_repository: LDMFCountriesRepositoryDep,
) -> LDMFCountryService:
    return LDMFCountryService(ldmf_countries_repository=ldmf_countries_repository)


LDMFCountryServiceDep = Annotated[LDMFCountryService, Depends(get_ldmf_country_service)]


def get_extracted_data_service(
    extracted_data_repository: ExtractedDataRepositoryDep,
    industry_data_service: IndustryDataServiceDep,
    role_data_service: RoleDataServiceDep,
    service_data_service: ServiceDataServiceDep,
    conversation_repository: ConversationRepositoryDep,
    quals_clients_repository: QualsClientsRepositoryDep,
    ldmf_country_service: LDMFCountryServiceDep,
) -> ExtractedDataService:
    return ExtractedDataService(
        extracted_data_repository=extracted_data_repository,
        conversation_repository=conversation_repository,
        quals_clients_repository=quals_clients_repository,
        industry_data_service=industry_data_service,
        role_data_service=role_data_service,
        service_data_service=service_data_service,
        ldmf_country_service=ldmf_country_service,
    )


ExtractedDataServiceDep = Annotated[ExtractedDataService, Depends(get_extracted_data_service)]


def get_kx_dash_service(
    kx_dash_repository: KXDashRepositoryDep,
    extracted_data_service: ExtractedDataServiceDep,
) -> KXDashService:
    return KXDashService(
        kx_dash_repository=kx_dash_repository,
        extracted_data_service=extracted_data_service,
    )


KXDashServiceDep = Annotated[KXDashService, Depends(get_kx_dash_service)]


def get_document_service(
    document_db_repository: DocumentDbRepositoryDep,
    document_blob_repository: DocumentBlobRepositoryDep,
    document_queue_repository: DocumentQueueRepositoryDep,
) -> DocumentService:
    return DocumentService(
        document_db_repository=document_db_repository,
        document_blob_repository=document_blob_repository,
        document_queue_repository=document_queue_repository,
    )


DocumentServiceDep = Annotated[DocumentService, Depends(get_document_service)]


def get_date_validator_service(openai_service: OpenAIRepositoryDep) -> DateValidatorService:
    return DateValidatorService(openai_service=openai_service)


DateValidatorServiceDep = Annotated[DateValidatorService, Depends(get_date_validator_service)]


def get_system_message_generation_service() -> SystemMessageGenerationService:
    """Get the system message generation service for dependency injection."""
    return SystemMessageGenerationService()


SystemMessageGenerationServiceDep = Annotated[
    SystemMessageGenerationService, Depends(get_system_message_generation_service)
]


def get_conversation_message_service(
    conversation_message_repository: ConversationMessageRepositoryDep,
    conversation_repository: ConversationRepositoryDep,
    document_service: DocumentServiceDep,
    document_db_repository: DocumentDbRepositoryDep,
    kx_dash_service: KXDashServiceDep,
    intent_classifier_service: IntentClassifierServiceDep,
    extracted_data_service: ExtractedDataServiceDep,
    date_validator_service: DateValidatorServiceDep,
    system_message_generation_service: SystemMessageGenerationServiceDep,
) -> ConversationMessageService:
    return ConversationMessageService(
        conversation_message_repository=conversation_message_repository,
        conversation_repository=conversation_repository,
        document_service=document_service,
        document_db_repository=document_db_repository,
        kx_dash_service=kx_dash_service,
        intent_classifier_service=intent_classifier_service,
        extracted_data_service=extracted_data_service,
        date_validator_service=date_validator_service,
        system_message_generation_service=system_message_generation_service,
    )


ConversationMessageServiceDep = Annotated[ConversationMessageService, Depends(get_conversation_message_service)]


def get_conversation_service(
    conversation_repository: ConversationRepositoryDep,
    conversation_message_service: ConversationMessageServiceDep,
    extracted_data_service: ExtractedDataServiceDep,
    document_service: DocumentServiceDep,
    kx_dash_service: KXDashServiceDep,
) -> ConversationService:
    return ConversationService(
        conversation_repository=conversation_repository,
        conversation_message_service=conversation_message_service,
        extracted_data_service=extracted_data_service,
        document_service=document_service,
        kx_dash_service=kx_dash_service,
    )


ConversationServiceDep = Annotated[ConversationService, Depends(get_conversation_service)]
