from datetime import datetime
from typing import List
from uuid import UUID

from pydantic import ConfigDict, Field

from constants.extracted_data import ConversationState
from core.schemas import CustomModel
from schemas.conversation_message import SystemMessageSerializer
from schemas.extracted_data import ExtractedData


__all__ = [
    'ConversationCreationRequest',
    'ConversationResponse',
    'ConversationWithWelcomeMessageResponse',
    'ConversationExtraData',
]


class ConversationCreationRequest(CustomModel):
    """Schema for creating a new conversation."""

    dash_activity_id: int | None


class ConversationResponse(CustomModel):
    """Schema for conversation response."""

    id: UUID = Field(validation_alias='PublicId')
    qual_id: str | None = Field(validation_alias='QualId')
    dash_activity_id: int | None = Field(validation_alias='DashActivityId')
    is_completed: bool = Field(validation_alias='IsCompleted')
    created_at: datetime = Field(validation_alias='CreatedAt')
    created_by_id: UUID = Field(validation_alias='CreatedById')
    created_by_name: str | None = Field(validation_alias='CreatedByName')

    model_config = ConfigDict(
        from_attributes=True,
    )


class ConversationWithWelcomeMessageResponse(CustomModel):
    """Schema for conversation with welcome message response."""

    conversation: ConversationResponse
    welcome_message: SystemMessageSerializer


class ConversationExtraData(ConversationResponse):
    confirmed_data: str | None = Field(validation_alias='ConfirmedData')
    state: ConversationState = Field(validation_alias='State')
    extracted_data: List[ExtractedData] | None = Field(validation_alias='ExtractedData')
