import json

from core.schemas import CustomModel


__all__ = ['ConfirmedData']


class ConfirmedData(CustomModel):
    """Schema for user-confirmed qual data."""

    client_name: str | None = None
    ldmf_country: str | None = None
    date_intervals: tuple[str | None, str | None] | None = None
    objective_and_scope: str | None = None
    outcomes: str | None = None
    proposed_client_name: str | None = None  # For tracking client names during creation flow

    @classmethod
    def from_json_string(cls, json_str: str | None) -> 'ConfirmedData':
        """Create ConfirmedData from JSON string stored in database."""
        if not json_str:
            return cls()
        try:
            data = json.loads(json_str)
            return cls.model_validate(data)
        except (json.JSONDecodeError, ValueError):
            return cls()

    def to_json_string(self) -> str:
        """Convert ConfirmedData to JSON string for database storage."""
        return json.dumps(self.model_dump(exclude_none=True), default=str)

    @property
    def required_fields_are_complete(self) -> bool:
        """Check if every field is not None and has values (non-empty for lists/strings)."""
        start_date, end_date = self.date_intervals or (None, None)
        return all(
            [
                self.client_name is not None and len(self.client_name.strip()) > 0,
                self.ldmf_country is not None and len(self.ldmf_country.strip()) > 0,
                start_date is not None,
                end_date is not None,
                self.objective_and_scope is not None and self.objective_and_scope.strip() != '',
                self.outcomes is not None and self.outcomes.strip() != '',
            ]
        )

    @property
    def is_empty(self) -> bool:
        return all(
            [
                self.client_name is None,
                self.ldmf_country is None,
                self.date_intervals is None,
                self.objective_and_scope is None,
                self.outcomes is None,
            ]
        )
