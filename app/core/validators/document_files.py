from fastapi import UploadFile


def validate_files_core(files: list[UploadFile] | None) -> list[UploadFile] | None:
    from config import settings  # Import your settings if needed

    for file in files or ():
        # Validate file has a name
        if not file.filename:
            raise ValueError('File must have a name')

        # Validate filename has content before extension
        filename = file.filename
        name_part = filename.rsplit('.', 1)[0] if '.' in filename else filename
        if not name_part:
            raise ValueError(f'File "{filename}" must have a name before extension')

        # Validate file size
        file_size = file.size or 0
        if file_size > settings.document_storage.max_file_size:
            max_size_expected = settings.document_storage.max_file_size
            msg = f'{file} size {file_size} bytes exceeds maximum allowed size of {max_size_expected} bytes'
            raise ValueError(msg)

        # Validate file type
        content_type = file.content_type
        if not content_type:
            raise ValueError(f'File "{file.filename}" must have a content type')

        file_formats_set = settings.document_storage.supported_file_formats
        if content_type not in file_formats_set:
            supported_formats = ', '.join(file_formats_set)
            msg = f'{file} has wrong type {content_type}. Expected {supported_formats}'
            raise ValueError(msg)

    return files
