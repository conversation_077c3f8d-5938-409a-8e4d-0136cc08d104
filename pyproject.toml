[tool.pyright]
include = ["app", "scripts"]
extraPaths = ["app"]
exclude = [
    "**/__pycache__"
]
pythonVersion = "3.12"
reportAttributeAccessIssue = "warning"
reportOptionalMemberAccess = "warning"
venvPath = "."
venv = ".venv"


[tool.ruff]
cache-dir = "~/.cache/ruff/kxqualsaiassistant"
exclude = [".venv"]
line-length = 120
src = ["app", "test", "scripts"]
ignore = ["E731"]

[tool.ruff.format]
quote-style = "single"

[tool.ruff.lint]
extend-select = [
    "I",    # isort
    "T201", # print
    "T203", # pprint
]

[tool.ruff.lint.isort]
combine-as-imports = true
force-sort-within-sections = true
lines-after-imports = 2

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F403"]

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["app/tests"]
python_files = ["test_*.py"]
python_functions = ["test_*"]
addopts = """
    --tb=short
    --asyncio-mode=auto
    --no-cov
"""
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]
asyncio_default_fixture_loop_scope = "session"
asyncio_default_test_loop_scope = "session"
